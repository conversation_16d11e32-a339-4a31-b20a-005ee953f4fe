import React from "react";
import {
  <PERSON>,
  CardContent,
  CardDescription,
  Card<PERSON><PERSON>er,
  CardTitle,
} from "@/components/ui/card";
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { But<PERSON> } from "@/components/ui/button";
import {
  PlusCircle,
  Activity,
  Zap,
  Wrench,
  GitBranch,
  Brain,
  Database,
  BarChart3,
  Settings,
} from "lucide-react";
import DashboardLayout from "@/components/dashboard/Layout";

export default function DashboardPage() {
  return (
    <DashboardLayout>
      <div className="p-6">
        {/* Header */}
        <header className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Dashboard</h1>
            <p className="text-muted-foreground">
              Welcome to SynapseAI orchestration platform.
            </p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline">Help</Button>
            <Button>
              <PlusCircle className="mr-2 h-4 w-4" />
              Create New
            </Button>
          </div>
        </header>

        {/* Quick Stats */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4 mb-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Total Agents
              </CardTitle>
              <Brain className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">12</div>
              <p className="text-xs text-muted-foreground">+2 from last week</p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Active Tools
              </CardTitle>
              <Wrench className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">24</div>
              <p className="text-xs text-muted-foreground">+4 from last week</p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Workflows</CardTitle>
              <GitBranch className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">7</div>
              <p className="text-xs text-muted-foreground">+1 from last week</p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">API Usage</CardTitle>
              <Activity className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">573</div>
              <p className="text-xs text-muted-foreground">
                +18% from last month
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Main Dashboard Tabs */}
        <Tabs defaultValue="overview" className="space-y-4">
          <TabsList>
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="analytics">Analytics</TabsTrigger>
            <TabsTrigger value="activity">Recent Activity</TabsTrigger>
            <TabsTrigger value="resources">Resources</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-4">
            {/* Quick Actions */}
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
              <Card>
                <CardHeader>
                  <CardTitle>Create New Agent</CardTitle>
                  <CardDescription>
                    Build an AI agent with custom prompt templates
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Button className="w-full">
                    <Brain className="mr-2 h-4 w-4" />
                    New Agent
                  </Button>
                </CardContent>
              </Card>
              <Card>
                <CardHeader>
                  <CardTitle>Create New Tool</CardTitle>
                  <CardDescription>
                    Build a tool with API integration capabilities
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Button className="w-full">
                    <Wrench className="mr-2 h-4 w-4" />
                    New Tool
                  </Button>
                </CardContent>
              </Card>
              <Card>
                <CardHeader>
                  <CardTitle>Create Workflow</CardTitle>
                  <CardDescription>
                    Combine agents and tools into a workflow
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Button className="w-full">
                    <GitBranch className="mr-2 h-4 w-4" />
                    New Workflow
                  </Button>
                </CardContent>
              </Card>
            </div>

            {/* Recent Resources */}
            <Card>
              <CardHeader>
                <CardTitle>Recent Resources</CardTitle>
                <CardDescription>
                  Your recently created or modified resources
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between border-b pb-2">
                    <div className="flex items-center">
                      <Brain className="mr-2 h-4 w-4" />
                      <div>
                        <p className="font-medium">Customer Support Agent</p>
                        <p className="text-sm text-muted-foreground">
                          Agent • Updated 2 hours ago
                        </p>
                      </div>
                    </div>
                    <Button variant="ghost" size="sm">
                      View
                    </Button>
                  </div>
                  <div className="flex items-center justify-between border-b pb-2">
                    <div className="flex items-center">
                      <Wrench className="mr-2 h-4 w-4" />
                      <div>
                        <p className="font-medium">Email Sender</p>
                        <p className="text-sm text-muted-foreground">
                          Tool • Created yesterday
                        </p>
                      </div>
                    </div>
                    <Button variant="ghost" size="sm">
                      View
                    </Button>
                  </div>
                  <div className="flex items-center justify-between border-b pb-2">
                    <div className="flex items-center">
                      <GitBranch className="mr-2 h-4 w-4" />
                      <div>
                        <p className="font-medium">Lead Qualification</p>
                        <p className="text-sm text-muted-foreground">
                          Workflow • Updated 3 days ago
                        </p>
                      </div>
                    </div>
                    <Button variant="ghost" size="sm">
                      View
                    </Button>
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <Database className="mr-2 h-4 w-4" />
                      <div>
                        <p className="font-medium">Product Knowledge Base</p>
                        <p className="text-sm text-muted-foreground">
                          Knowledge Base • Updated 5 days ago
                        </p>
                      </div>
                    </div>
                    <Button variant="ghost" size="sm">
                      View
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="analytics" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Usage Analytics</CardTitle>
                <CardDescription>Your platform usage over time</CardDescription>
              </CardHeader>
              <CardContent className="h-80 flex items-center justify-center">
                <div className="text-center">
                  <BarChart3 className="h-16 w-16 mx-auto text-muted-foreground" />
                  <p className="mt-2 text-muted-foreground">
                    Analytics visualization would appear here
                  </p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="activity" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Recent Activity</CardTitle>
                <CardDescription>
                  Latest actions across your organization
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {[
                    {
                      user: "Alex Kim",
                      action: "created a new agent",
                      resource: "Data Analysis Assistant",
                      time: "10 minutes ago",
                    },
                    {
                      user: "Jamie Smith",
                      action: "updated workflow",
                      resource: "Customer Onboarding",
                      time: "1 hour ago",
                    },
                    {
                      user: "Taylor Wong",
                      action: "approved HITL request",
                      resource: "Payment Processing",
                      time: "3 hours ago",
                    },
                    {
                      user: "Morgan Lee",
                      action: "added document to knowledge base",
                      resource: "Product Documentation",
                      time: "5 hours ago",
                    },
                    {
                      user: "Casey Johnson",
                      action: "created new tool",
                      resource: "Slack Integration",
                      time: "Yesterday",
                    },
                  ].map((item, i) => (
                    <div
                      key={i}
                      className="flex items-center justify-between border-b pb-2 last:border-0"
                    >
                      <div>
                        <p className="font-medium">
                          {item.user} {item.action}
                        </p>
                        <p className="text-sm text-muted-foreground">
                          {item.resource} • {item.time}
                        </p>
                      </div>
                      <Button variant="ghost" size="sm">
                        View
                      </Button>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="resources" className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
              <Card>
                <CardHeader>
                  <CardTitle>Agents</CardTitle>
                  <CardDescription>Manage your AI agents</CardDescription>
                </CardHeader>
                <CardContent>
                  <Button className="w-full">View All Agents</Button>
                </CardContent>
              </Card>
              <Card>
                <CardHeader>
                  <CardTitle>Tools</CardTitle>
                  <CardDescription>
                    Manage your integration tools
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Button className="w-full">View All Tools</Button>
                </CardContent>
              </Card>
              <Card>
                <CardHeader>
                  <CardTitle>Workflows</CardTitle>
                  <CardDescription>
                    Manage your hybrid workflows
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Button className="w-full">View All Workflows</Button>
                </CardContent>
              </Card>
              <Card>
                <CardHeader>
                  <CardTitle>Knowledge Base</CardTitle>
                  <CardDescription>
                    Manage your knowledge documents
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Button className="w-full">View Knowledge Base</Button>
                </CardContent>
              </Card>
              <Card>
                <CardHeader>
                  <CardTitle>Widgets</CardTitle>
                  <CardDescription>
                    Manage your embeddable widgets
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Button className="w-full">View All Widgets</Button>
                </CardContent>
              </Card>
              <Card>
                <CardHeader>
                  <CardTitle>Settings</CardTitle>
                  <CardDescription>
                    Manage organization settings
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Button className="w-full">
                    <Settings className="mr-2 h-4 w-4" />
                    Open Settings
                  </Button>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  );
}
