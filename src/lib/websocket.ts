"use client";

import { toast } from "@/components/ui/use-toast";

interface WebSocketMessage {
  type: string;
  data: any;
  timestamp: string;
}

class WebSocketManager {
  private ws: WebSocket | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000;
  private listeners: Map<string, ((data: any) => void)[]> = new Map();

  connect(url?: string) {
    if (typeof window === "undefined") return;

    const wsUrl =
      url ||
      `${window.location.protocol === "https:" ? "wss:" : "ws:"}//${window.location.host}/ws`;

    try {
      this.ws = new WebSocket(wsUrl);

      this.ws.onopen = () => {
        console.log("WebSocket connected");
        this.reconnectAttempts = 0;
        toast({
          title: "Connected",
          description: "Real-time updates are now active",
        });
      };

      this.ws.onmessage = (event) => {
        try {
          const message: WebSocketMessage = JSON.parse(event.data);
          this.handleMessage(message);
        } catch (error) {
          console.error("Failed to parse WebSocket message:", error);
        }
      };

      this.ws.onclose = () => {
        console.log("WebSocket disconnected");
        this.handleReconnect();
      };

      this.ws.onerror = (error) => {
        console.error("WebSocket error:", error);
      };
    } catch (error) {
      console.error("Failed to connect to WebSocket:", error);
    }
  }

  private handleMessage(message: WebSocketMessage) {
    const listeners = this.listeners.get(message.type) || [];
    listeners.forEach((listener) => {
      try {
        listener(message.data);
      } catch (error) {
        console.error("Error in WebSocket listener:", error);
      }
    });

    // Handle common message types
    switch (message.type) {
      case "agent.response":
        toast({
          title: "Agent Response",
          description: `Agent "${message.data.agentName}" completed processing`,
        });
        break;
      case "workflow.completed":
        toast({
          title: "Workflow Completed",
          description: `Workflow "${message.data.workflowName}" finished successfully`,
        });
        break;
      case "workflow.failed":
        toast({
          title: "Workflow Failed",
          description: `Workflow "${message.data.workflowName}" encountered an error`,
          variant: "destructive",
        });
        break;
      case "user.joined":
        toast({
          title: "User Joined",
          description: `${message.data.userName} joined the workspace`,
        });
        break;
    }
  }

  private handleReconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      const delay =
        this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);

      setTimeout(() => {
        console.log(
          `Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts})`,
        );
        this.connect();
      }, delay);
    } else {
      toast({
        title: "Connection Lost",
        description: "Unable to reconnect to real-time updates",
        variant: "destructive",
      });
    }
  }

  subscribe(eventType: string, callback: (data: any) => void) {
    if (!this.listeners.has(eventType)) {
      this.listeners.set(eventType, []);
    }
    this.listeners.get(eventType)!.push(callback);
  }

  unsubscribe(eventType: string, callback: (data: any) => void) {
    const listeners = this.listeners.get(eventType);
    if (listeners) {
      const index = listeners.indexOf(callback);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  }

  send(type: string, data: any) {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      const message: WebSocketMessage = {
        type,
        data,
        timestamp: new Date().toISOString(),
      };
      this.ws.send(JSON.stringify(message));
    } else {
      console.warn("WebSocket is not connected");
    }
  }

  disconnect() {
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
  }

  isConnected(): boolean {
    return this.ws?.readyState === WebSocket.OPEN;
  }
}

// Singleton instance
const wsManager = new WebSocketManager();

// Auto-connect when in browser
if (typeof window !== "undefined") {
  // Connect after a short delay to ensure the app is initialized
  setTimeout(() => {
    wsManager.connect();
  }, 1000);
}

export default wsManager;

// Convenience hooks for React components
export const useWebSocket = () => {
  return {
    subscribe: wsManager.subscribe.bind(wsManager),
    unsubscribe: wsManager.unsubscribe.bind(wsManager),
    send: wsManager.send.bind(wsManager),
    isConnected: wsManager.isConnected.bind(wsManager),
  };
};
