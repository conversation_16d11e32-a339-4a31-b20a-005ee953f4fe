"use client";

import React, { useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Tabs, <PERSON>bsContent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import {
  Key,
  Copy,
  Eye,
  EyeOff,
  Plus,
  Trash2,
  RefreshCw,
  Code,
  Webhook,
  Globe,
  Settings,
  CheckCircle,
  AlertTriangle,
  ExternalLink,
  Download,
  Upload,
} from "lucide-react";

interface IntegrationSettingsProps {
  organizationId?: string;
}

interface ApiKey {
  id: string;
  name: string;
  key: string;
  permissions: string[];
  createdAt: string;
  lastUsed?: string;
  isActive: boolean;
}

interface Widget {
  id: string;
  name: string;
  type: "chat" | "form" | "embed";
  agentId: string;
  embedCode: string;
  isActive: boolean;
  createdAt: string;
  views: number;
}

interface Webhook {
  id: string;
  name: string;
  url: string;
  events: string[];
  secret: string;
  isActive: boolean;
  createdAt: string;
  lastTriggered?: string;
  status: "active" | "failed" | "pending";
}

const IntegrationSettings = ({
  organizationId = "org_123456",
}: IntegrationSettingsProps) => {
  const [activeTab, setActiveTab] = useState("api-keys");
  const [showApiKeyDialog, setShowApiKeyDialog] = useState(false);
  const [showWidgetDialog, setShowWidgetDialog] = useState(false);
  const [showWebhookDialog, setShowWebhookDialog] = useState(false);
  const [visibleKeys, setVisibleKeys] = useState<Record<string, boolean>>({});

  // Mock data
  const [apiKeys, setApiKeys] = useState<ApiKey[]>([
    {
      id: "key_1",
      name: "Production API Key",
      key: "sk_live_1234567890abcdef",
      permissions: ["agents:read", "agents:write", "workflows:execute"],
      createdAt: "2023-06-01T10:00:00Z",
      lastUsed: "2023-06-15T14:30:00Z",
      isActive: true,
    },
    {
      id: "key_2",
      name: "Development API Key",
      key: "sk_test_abcdef1234567890",
      permissions: ["agents:read", "tools:read"],
      createdAt: "2023-05-15T09:00:00Z",
      lastUsed: "2023-06-14T16:45:00Z",
      isActive: true,
    },
  ]);

  const [widgets, setWidgets] = useState<Widget[]>([
    {
      id: "widget_1",
      name: "Customer Support Chat",
      type: "chat",
      agentId: "agent_1",
      embedCode:
        '<script src="https://api.synapseai.com/widget/widget_1.js"></script>',
      isActive: true,
      createdAt: "2023-06-01T10:00:00Z",
      views: 1247,
    },
    {
      id: "widget_2",
      name: "Lead Qualification Form",
      type: "form",
      agentId: "agent_2",
      embedCode:
        '<iframe src="https://api.synapseai.com/embed/widget_2" width="400" height="600"></iframe>',
      isActive: true,
      createdAt: "2023-05-20T14:00:00Z",
      views: 892,
    },
  ]);

  const [webhooks, setWebhooks] = useState<Webhook[]>([
    {
      id: "webhook_1",
      name: "Workflow Completion",
      url: "https://api.example.com/webhooks/workflow-complete",
      events: ["workflow.completed", "workflow.failed"],
      secret: "whsec_1234567890abcdef",
      isActive: true,
      createdAt: "2023-06-01T10:00:00Z",
      lastTriggered: "2023-06-15T14:30:00Z",
      status: "active",
    },
    {
      id: "webhook_2",
      name: "Agent Deployment",
      url: "https://api.example.com/webhooks/agent-deploy",
      events: ["agent.deployed", "agent.failed"],
      secret: "whsec_abcdef1234567890",
      isActive: false,
      createdAt: "2023-05-15T09:00:00Z",
      status: "pending",
    },
  ]);

  const [newApiKey, setNewApiKey] = useState({
    name: "",
    permissions: [] as string[],
  });

  const [newWidget, setNewWidget] = useState({
    name: "",
    type: "chat" as "chat" | "form" | "embed",
    agentId: "",
  });

  const [newWebhook, setNewWebhook] = useState({
    name: "",
    url: "",
    events: [] as string[],
  });

  const availablePermissions = [
    "agents:read",
    "agents:write",
    "tools:read",
    "tools:write",
    "workflows:read",
    "workflows:write",
    "workflows:execute",
    "knowledge:read",
    "knowledge:write",
    "analytics:read",
  ];

  const availableEvents = [
    "agent.created",
    "agent.updated",
    "agent.deployed",
    "agent.failed",
    "tool.created",
    "tool.updated",
    "workflow.created",
    "workflow.updated",
    "workflow.started",
    "workflow.completed",
    "workflow.failed",
    "knowledge.document_added",
    "knowledge.document_processed",
  ];

  const mockAgents = [
    { id: "agent_1", name: "Customer Support Agent" },
    { id: "agent_2", name: "Lead Qualification Agent" },
    { id: "agent_3", name: "Data Analysis Agent" },
  ];

  const toggleKeyVisibility = (keyId: string) => {
    setVisibleKeys((prev) => ({ ...prev, [keyId]: !prev[keyId] }));
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    // In a real app, you'd show a toast notification here
  };

  const generateApiKey = () => {
    const newKey: ApiKey = {
      id: `key_${Date.now()}`,
      name: newApiKey.name,
      key: `sk_${Math.random().toString(36).substring(2, 15)}${Math.random().toString(36).substring(2, 15)}`,
      permissions: newApiKey.permissions,
      createdAt: new Date().toISOString(),
      isActive: true,
    };
    setApiKeys([...apiKeys, newKey]);
    setNewApiKey({ name: "", permissions: [] });
    setShowApiKeyDialog(false);
  };

  const generateWidget = () => {
    const widgetId = `widget_${Date.now()}`;
    const newWidgetData: Widget = {
      id: widgetId,
      name: newWidget.name,
      type: newWidget.type,
      agentId: newWidget.agentId,
      embedCode:
        newWidget.type === "chat"
          ? `<script src="https://api.synapseai.com/widget/${widgetId}.js"></script>`
          : `<iframe src="https://api.synapseai.com/embed/${widgetId}" width="400" height="600"></iframe>`,
      isActive: true,
      createdAt: new Date().toISOString(),
      views: 0,
    };
    setWidgets([...widgets, newWidgetData]);
    setNewWidget({ name: "", type: "chat", agentId: "" });
    setShowWidgetDialog(false);
  };

  const createWebhook = () => {
    const newWebhookData: Webhook = {
      id: `webhook_${Date.now()}`,
      name: newWebhook.name,
      url: newWebhook.url,
      events: newWebhook.events,
      secret: `whsec_${Math.random().toString(36).substring(2, 15)}${Math.random().toString(36).substring(2, 15)}`,
      isActive: true,
      createdAt: new Date().toISOString(),
      status: "pending",
    };
    setWebhooks([...webhooks, newWebhookData]);
    setNewWebhook({ name: "", url: "", events: [] });
    setShowWebhookDialog(false);
  };

  const deleteApiKey = (keyId: string) => {
    setApiKeys(apiKeys.filter((key) => key.id !== keyId));
  };

  const deleteWidget = (widgetId: string) => {
    setWidgets(widgets.filter((widget) => widget.id !== widgetId));
  };

  const deleteWebhook = (webhookId: string) => {
    setWebhooks(webhooks.filter((webhook) => webhook.id !== webhookId));
  };

  const toggleApiKeyStatus = (keyId: string) => {
    setApiKeys(
      apiKeys.map((key) =>
        key.id === keyId ? { ...key, isActive: !key.isActive } : key,
      ),
    );
  };

  const toggleWidgetStatus = (widgetId: string) => {
    setWidgets(
      widgets.map((widget) =>
        widget.id === widgetId
          ? { ...widget, isActive: !widget.isActive }
          : widget,
      ),
    );
  };

  const toggleWebhookStatus = (webhookId: string) => {
    setWebhooks(
      webhooks.map((webhook) =>
        webhook.id === webhookId
          ? { ...webhook, isActive: !webhook.isActive }
          : webhook,
      ),
    );
  };

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold">Integration Settings</h1>
          <p className="text-muted-foreground">
            Manage API keys, embeddable widgets, and webhook configurations
          </p>
        </div>
        <Button variant="outline">
          <Download className="mr-2 h-4 w-4" />
          Export Configuration
        </Button>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="mb-6">
          <TabsTrigger value="api-keys">API Keys</TabsTrigger>
          <TabsTrigger value="widgets">Embeddable Widgets</TabsTrigger>
          <TabsTrigger value="webhooks">Webhooks</TabsTrigger>
          <TabsTrigger value="settings">General Settings</TabsTrigger>
        </TabsList>

        <TabsContent value="api-keys" className="space-y-6">
          <div className="flex justify-between items-center">
            <div>
              <h2 className="text-lg font-semibold">API Keys</h2>
              <p className="text-sm text-muted-foreground">
                Generate and manage API keys for programmatic access
              </p>
            </div>
            <Dialog open={showApiKeyDialog} onOpenChange={setShowApiKeyDialog}>
              <DialogTrigger asChild>
                <Button>
                  <Plus className="mr-2 h-4 w-4" />
                  Generate API Key
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Generate New API Key</DialogTitle>
                  <DialogDescription>
                    Create a new API key with specific permissions for your
                    application.
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="key-name">Key Name</Label>
                    <Input
                      id="key-name"
                      value={newApiKey.name}
                      onChange={(e) =>
                        setNewApiKey({ ...newApiKey, name: e.target.value })
                      }
                      placeholder="Production API Key"
                    />
                  </div>
                  <div>
                    <Label>Permissions</Label>
                    <div className="grid grid-cols-2 gap-2 mt-2">
                      {availablePermissions.map((permission) => (
                        <div
                          key={permission}
                          className="flex items-center space-x-2"
                        >
                          <input
                            type="checkbox"
                            id={permission}
                            checked={newApiKey.permissions.includes(permission)}
                            onChange={(e) => {
                              if (e.target.checked) {
                                setNewApiKey({
                                  ...newApiKey,
                                  permissions: [
                                    ...newApiKey.permissions,
                                    permission,
                                  ],
                                });
                              } else {
                                setNewApiKey({
                                  ...newApiKey,
                                  permissions: newApiKey.permissions.filter(
                                    (p) => p !== permission,
                                  ),
                                });
                              }
                            }}
                          />
                          <Label htmlFor={permission} className="text-sm">
                            {permission}
                          </Label>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
                <DialogFooter>
                  <Button
                    variant="outline"
                    onClick={() => setShowApiKeyDialog(false)}
                  >
                    Cancel
                  </Button>
                  <Button
                    onClick={generateApiKey}
                    disabled={
                      !newApiKey.name || newApiKey.permissions.length === 0
                    }
                  >
                    Generate Key
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>

          <div className="space-y-4">
            {apiKeys.map((apiKey) => (
              <Card key={apiKey.id}>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div>
                      <CardTitle className="text-lg">{apiKey.name}</CardTitle>
                      <CardDescription>
                        Created{" "}
                        {new Date(apiKey.createdAt).toLocaleDateString()}
                        {apiKey.lastUsed &&
                          ` • Last used ${new Date(apiKey.lastUsed).toLocaleDateString()}`}
                      </CardDescription>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Badge
                        variant={apiKey.isActive ? "default" : "secondary"}
                      >
                        {apiKey.isActive ? "Active" : "Inactive"}
                      </Badge>
                      <Switch
                        checked={apiKey.isActive}
                        onCheckedChange={() => toggleApiKeyStatus(apiKey.id)}
                      />
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label>API Key</Label>
                    <div className="flex items-center space-x-2 mt-1">
                      <Input
                        value={
                          visibleKeys[apiKey.id]
                            ? apiKey.key
                            : "•".repeat(apiKey.key.length)
                        }
                        readOnly
                        className="font-mono"
                      />
                      <Button
                        variant="outline"
                        size="icon"
                        onClick={() => toggleKeyVisibility(apiKey.id)}
                      >
                        {visibleKeys[apiKey.id] ? (
                          <EyeOff className="h-4 w-4" />
                        ) : (
                          <Eye className="h-4 w-4" />
                        )}
                      </Button>
                      <Button
                        variant="outline"
                        size="icon"
                        onClick={() => copyToClipboard(apiKey.key)}
                      >
                        <Copy className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                  <div>
                    <Label>Permissions</Label>
                    <div className="flex flex-wrap gap-2 mt-1">
                      {apiKey.permissions.map((permission) => (
                        <Badge key={permission} variant="outline">
                          {permission}
                        </Badge>
                      ))}
                    </div>
                  </div>
                  <div className="flex justify-end space-x-2">
                    <Button variant="outline" size="sm">
                      <RefreshCw className="mr-2 h-4 w-4" />
                      Regenerate
                    </Button>
                    <AlertDialog>
                      <AlertDialogTrigger asChild>
                        <Button variant="destructive" size="sm">
                          <Trash2 className="mr-2 h-4 w-4" />
                          Delete
                        </Button>
                      </AlertDialogTrigger>
                      <AlertDialogContent>
                        <AlertDialogHeader>
                          <AlertDialogTitle>Delete API Key</AlertDialogTitle>
                          <AlertDialogDescription>
                            This action cannot be undone. Applications using
                            this API key will lose access immediately.
                          </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                          <AlertDialogCancel>Cancel</AlertDialogCancel>
                          <AlertDialogAction
                            onClick={() => deleteApiKey(apiKey.id)}
                          >
                            Delete
                          </AlertDialogAction>
                        </AlertDialogFooter>
                      </AlertDialogContent>
                    </AlertDialog>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="widgets" className="space-y-6">
          <div className="flex justify-between items-center">
            <div>
              <h2 className="text-lg font-semibold">Embeddable Widgets</h2>
              <p className="text-sm text-muted-foreground">
                Create widgets to embed your agents on external websites
              </p>
            </div>
            <Dialog open={showWidgetDialog} onOpenChange={setShowWidgetDialog}>
              <DialogTrigger asChild>
                <Button>
                  <Plus className="mr-2 h-4 w-4" />
                  Create Widget
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Create New Widget</DialogTitle>
                  <DialogDescription>
                    Create an embeddable widget for your agent.
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="widget-name">Widget Name</Label>
                    <Input
                      id="widget-name"
                      value={newWidget.name}
                      onChange={(e) =>
                        setNewWidget({ ...newWidget, name: e.target.value })
                      }
                      placeholder="Customer Support Chat"
                    />
                  </div>
                  <div>
                    <Label htmlFor="widget-type">Widget Type</Label>
                    <Select
                      value={newWidget.type}
                      onValueChange={(value: "chat" | "form" | "embed") =>
                        setNewWidget({ ...newWidget, type: value })
                      }
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select widget type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="chat">Chat Widget</SelectItem>
                        <SelectItem value="form">Form Widget</SelectItem>
                        <SelectItem value="embed">Embed Widget</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="widget-agent">Agent</Label>
                    <Select
                      value={newWidget.agentId}
                      onValueChange={(value) =>
                        setNewWidget({ ...newWidget, agentId: value })
                      }
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select agent" />
                      </SelectTrigger>
                      <SelectContent>
                        {mockAgents.map((agent) => (
                          <SelectItem key={agent.id} value={agent.id}>
                            {agent.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <DialogFooter>
                  <Button
                    variant="outline"
                    onClick={() => setShowWidgetDialog(false)}
                  >
                    Cancel
                  </Button>
                  <Button
                    onClick={generateWidget}
                    disabled={!newWidget.name || !newWidget.agentId}
                  >
                    Create Widget
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>

          <div className="space-y-4">
            {widgets.map((widget) => (
              <Card key={widget.id}>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div>
                      <CardTitle className="text-lg">{widget.name}</CardTitle>
                      <CardDescription>
                        {widget.type.charAt(0).toUpperCase() +
                          widget.type.slice(1)}{" "}
                        Widget • {widget.views} views • Created{" "}
                        {new Date(widget.createdAt).toLocaleDateString()}
                      </CardDescription>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Badge
                        variant={widget.isActive ? "default" : "secondary"}
                      >
                        {widget.isActive ? "Active" : "Inactive"}
                      </Badge>
                      <Switch
                        checked={widget.isActive}
                        onCheckedChange={() => toggleWidgetStatus(widget.id)}
                      />
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label>Embed Code</Label>
                    <div className="flex items-center space-x-2 mt-1">
                      <Textarea
                        value={widget.embedCode}
                        readOnly
                        className="font-mono text-sm"
                        rows={3}
                      />
                      <Button
                        variant="outline"
                        size="icon"
                        onClick={() => copyToClipboard(widget.embedCode)}
                      >
                        <Copy className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                  <div className="flex justify-end space-x-2">
                    <Button variant="outline" size="sm">
                      <ExternalLink className="mr-2 h-4 w-4" />
                      Preview
                    </Button>
                    <Button variant="outline" size="sm">
                      <Settings className="mr-2 h-4 w-4" />
                      Configure
                    </Button>
                    <AlertDialog>
                      <AlertDialogTrigger asChild>
                        <Button variant="destructive" size="sm">
                          <Trash2 className="mr-2 h-4 w-4" />
                          Delete
                        </Button>
                      </AlertDialogTrigger>
                      <AlertDialogContent>
                        <AlertDialogHeader>
                          <AlertDialogTitle>Delete Widget</AlertDialogTitle>
                          <AlertDialogDescription>
                            This action cannot be undone. The widget will stop
                            working on all websites where it's embedded.
                          </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                          <AlertDialogCancel>Cancel</AlertDialogCancel>
                          <AlertDialogAction
                            onClick={() => deleteWidget(widget.id)}
                          >
                            Delete
                          </AlertDialogAction>
                        </AlertDialogFooter>
                      </AlertDialogContent>
                    </AlertDialog>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="webhooks" className="space-y-6">
          <div className="flex justify-between items-center">
            <div>
              <h2 className="text-lg font-semibold">Webhooks</h2>
              <p className="text-sm text-muted-foreground">
                Configure webhooks to receive real-time notifications about
                events
              </p>
            </div>
            <Dialog
              open={showWebhookDialog}
              onOpenChange={setShowWebhookDialog}
            >
              <DialogTrigger asChild>
                <Button>
                  <Plus className="mr-2 h-4 w-4" />
                  Add Webhook
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-2xl">
                <DialogHeader>
                  <DialogTitle>Add New Webhook</DialogTitle>
                  <DialogDescription>
                    Configure a webhook endpoint to receive event notifications.
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="webhook-name">Webhook Name</Label>
                    <Input
                      id="webhook-name"
                      value={newWebhook.name}
                      onChange={(e) =>
                        setNewWebhook({ ...newWebhook, name: e.target.value })
                      }
                      placeholder="Workflow Completion Webhook"
                    />
                  </div>
                  <div>
                    <Label htmlFor="webhook-url">Endpoint URL</Label>
                    <Input
                      id="webhook-url"
                      value={newWebhook.url}
                      onChange={(e) =>
                        setNewWebhook({ ...newWebhook, url: e.target.value })
                      }
                      placeholder="https://api.example.com/webhooks/synapseai"
                    />
                  </div>
                  <div>
                    <Label>Events to Subscribe</Label>
                    <ScrollArea className="h-48 border rounded-md p-4 mt-2">
                      <div className="grid grid-cols-2 gap-2">
                        {availableEvents.map((event) => (
                          <div
                            key={event}
                            className="flex items-center space-x-2"
                          >
                            <input
                              type="checkbox"
                              id={event}
                              checked={newWebhook.events.includes(event)}
                              onChange={(e) => {
                                if (e.target.checked) {
                                  setNewWebhook({
                                    ...newWebhook,
                                    events: [...newWebhook.events, event],
                                  });
                                } else {
                                  setNewWebhook({
                                    ...newWebhook,
                                    events: newWebhook.events.filter(
                                      (e) => e !== event,
                                    ),
                                  });
                                }
                              }}
                            />
                            <Label htmlFor={event} className="text-sm">
                              {event}
                            </Label>
                          </div>
                        ))}
                      </div>
                    </ScrollArea>
                  </div>
                </div>
                <DialogFooter>
                  <Button
                    variant="outline"
                    onClick={() => setShowWebhookDialog(false)}
                  >
                    Cancel
                  </Button>
                  <Button
                    onClick={createWebhook}
                    disabled={
                      !newWebhook.name ||
                      !newWebhook.url ||
                      newWebhook.events.length === 0
                    }
                  >
                    Create Webhook
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>

          <div className="space-y-4">
            {webhooks.map((webhook) => (
              <Card key={webhook.id}>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div>
                      <CardTitle className="text-lg">{webhook.name}</CardTitle>
                      <CardDescription>
                        {webhook.url} • Created{" "}
                        {new Date(webhook.createdAt).toLocaleDateString()}
                        {webhook.lastTriggered &&
                          ` • Last triggered ${new Date(webhook.lastTriggered).toLocaleDateString()}`}
                      </CardDescription>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Badge
                        variant={
                          webhook.status === "active"
                            ? "default"
                            : webhook.status === "failed"
                              ? "destructive"
                              : "secondary"
                        }
                      >
                        {webhook.status === "active" && (
                          <CheckCircle className="mr-1 h-3 w-3" />
                        )}
                        {webhook.status === "failed" && (
                          <AlertTriangle className="mr-1 h-3 w-3" />
                        )}
                        {webhook.status.charAt(0).toUpperCase() +
                          webhook.status.slice(1)}
                      </Badge>
                      <Switch
                        checked={webhook.isActive}
                        onCheckedChange={() => toggleWebhookStatus(webhook.id)}
                      />
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label>Webhook Secret</Label>
                    <div className="flex items-center space-x-2 mt-1">
                      <Input
                        value={webhook.secret}
                        readOnly
                        className="font-mono"
                      />
                      <Button
                        variant="outline"
                        size="icon"
                        onClick={() => copyToClipboard(webhook.secret)}
                      >
                        <Copy className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                  <div>
                    <Label>Subscribed Events</Label>
                    <div className="flex flex-wrap gap-2 mt-1">
                      {webhook.events.map((event) => (
                        <Badge key={event} variant="outline">
                          {event}
                        </Badge>
                      ))}
                    </div>
                  </div>
                  <div className="flex justify-end space-x-2">
                    <Button variant="outline" size="sm">
                      <Webhook className="mr-2 h-4 w-4" />
                      Test Webhook
                    </Button>
                    <Button variant="outline" size="sm">
                      <Settings className="mr-2 h-4 w-4" />
                      Configure
                    </Button>
                    <AlertDialog>
                      <AlertDialogTrigger asChild>
                        <Button variant="destructive" size="sm">
                          <Trash2 className="mr-2 h-4 w-4" />
                          Delete
                        </Button>
                      </AlertDialogTrigger>
                      <AlertDialogContent>
                        <AlertDialogHeader>
                          <AlertDialogTitle>Delete Webhook</AlertDialogTitle>
                          <AlertDialogDescription>
                            This action cannot be undone. You will stop
                            receiving event notifications at this endpoint.
                          </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                          <AlertDialogCancel>Cancel</AlertDialogCancel>
                          <AlertDialogAction
                            onClick={() => deleteWebhook(webhook.id)}
                          >
                            Delete
                          </AlertDialogAction>
                        </AlertDialogFooter>
                      </AlertDialogContent>
                    </AlertDialog>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="settings" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>General Integration Settings</CardTitle>
              <CardDescription>
                Configure global settings for integrations and API access
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex items-center justify-between">
                <div>
                  <Label className="text-base">Rate Limiting</Label>
                  <p className="text-sm text-muted-foreground">
                    Enable rate limiting for API requests
                  </p>
                </div>
                <Switch defaultChecked />
              </div>
              <Separator />
              <div className="flex items-center justify-between">
                <div>
                  <Label className="text-base">IP Whitelisting</Label>
                  <p className="text-sm text-muted-foreground">
                    Restrict API access to specific IP addresses
                  </p>
                </div>
                <Switch />
              </div>
              <Separator />
              <div className="flex items-center justify-between">
                <div>
                  <Label className="text-base">CORS Configuration</Label>
                  <p className="text-sm text-muted-foreground">
                    Configure Cross-Origin Resource Sharing settings
                  </p>
                </div>
                <Button variant="outline">Configure</Button>
              </div>
              <Separator />
              <div className="flex items-center justify-between">
                <div>
                  <Label className="text-base">Webhook Retry Policy</Label>
                  <p className="text-sm text-muted-foreground">
                    Configure retry attempts for failed webhook deliveries
                  </p>
                </div>
                <Button variant="outline">Configure</Button>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Security Settings</CardTitle>
              <CardDescription>
                Advanced security configurations for your integrations
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex items-center justify-between">
                <div>
                  <Label className="text-base">API Key Rotation</Label>
                  <p className="text-sm text-muted-foreground">
                    Automatically rotate API keys every 90 days
                  </p>
                </div>
                <Switch />
              </div>
              <Separator />
              <div className="flex items-center justify-between">
                <div>
                  <Label className="text-base">
                    Webhook Signature Verification
                  </Label>
                  <p className="text-sm text-muted-foreground">
                    Require signature verification for all webhook payloads
                  </p>
                </div>
                <Switch defaultChecked />
              </div>
              <Separator />
              <div className="flex items-center justify-between">
                <div>
                  <Label className="text-base">Audit Logging</Label>
                  <p className="text-sm text-muted-foreground">
                    Log all API requests and webhook deliveries
                  </p>
                </div>
                <Switch defaultChecked />
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default IntegrationSettings;
