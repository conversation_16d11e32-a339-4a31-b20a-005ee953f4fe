"use client";

import React, { useState } from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Separator } from "@/components/ui/separator";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  ChevronDown,
  ChevronRight,
  Home,
  Bot,
  Wrench,
  GitBranch,
  Database,
  BarChart3,
  Settings,
  Users,
  CreditCard,
  Bell,
  <PERSON>u,
  <PERSON>g<PERSON><PERSON>,
  <PERSON>Circle,
  <PERSON><PERSON><PERSON>,
  <PERSON>ers,
  FileText,
  <PERSON>,
  Gauge,
  Puzzle,
} from "lucide-react";

interface NavigationItem {
  id: string;
  name: string;
  icon: React.ReactNode;
  href: string;
  badge?: number;
}

interface NavigationCategory {
  category: string;
  title: string;
  items: NavigationItem[];
}

interface SidebarProps {
  className?: string;
  collapsed?: boolean;
  onToggleCollapse?: () => void;
}

export default function Sidebar({
  className,
  collapsed = false,
  onToggleCollapse,
}: SidebarProps) {
  const pathname = usePathname();
  const [expandedCategories, setExpandedCategories] = useState<
    Record<string, boolean>
  >({
    agents: true,
    tools: true,
    workflows: true,
    knowledge: true,
    analytics: true,
    admin: true,
  });

  // Helper function to determine if a navigation item is active
  const isItemActive = (itemHref: string, itemId: string) => {
    // Exact match for dashboard
    if (itemId === "dashboard") {
      return pathname === "/dashboard";
    }

    // For other items, check if current path starts with the item href
    // or if it's an exact match
    return pathname === itemHref || pathname.startsWith(itemHref + "/");
  };

  // Helper function to check if a category has any active items
  const isCategoryActive = (category: NavigationCategory) => {
    return category.items.some((item: NavigationItem) => isItemActive(item.href, item.id));
  };

  const toggleCategory = (category: string) => {
    setExpandedCategories((prev) => ({
      ...prev,
      [category]: !prev[category],
    }));
  };

  const organizations = [
    { id: "org1", name: "Acme Corp", role: "Admin" },
    { id: "org2", name: "Globex Inc", role: "Developer" },
    { id: "org3", name: "Initech", role: "Viewer" },
  ];

  const [selectedOrg, setSelectedOrg] = useState(organizations[0]);

  const navigationItems: NavigationCategory[] = [
    {
      category: "main",
      title: "Main",
      items: [{ id: "dashboard", name: "Dashboard", icon: <Home size={20} />, href: "/dashboard" }],
    },
    {
      category: "agents",
      title: "Agents",
      items: [
        { id: "agent-builder", name: "Agent Builder", icon: <Bot size={20} />, href: "/dashboard/agents/builder" },
        {
          id: "prompt-templates",
          name: "Prompt Templates",
          icon: <FileText size={20} />,
          href: "/dashboard/agents",
        },
        {
          id: "agent-marketplace",
          name: "Marketplace",
          icon: <Sparkles size={20} />,
          href: "/dashboard/agents",
        },
      ],
    },
    {
      category: "tools",
      title: "Tools",
      items: [
        {
          id: "tool-builder",
          name: "Tool Builder",
          icon: <Wrench size={20} />,
          href: "/dashboard/tools/builder",
        },
        {
          id: "api-integrations",
          name: "API Integrations",
          icon: <Code size={20} />,
          href: "/dashboard/tools/integrations",
        },
        {
          id: "tool-marketplace",
          name: "Marketplace",
          icon: <Puzzle size={20} />,
          href: "/dashboard/tools",
        },
      ],
    },
    {
      category: "workflows",
      title: "Workflows",
      items: [
        {
          id: "workflow-builder",
          name: "Workflow Builder",
          icon: <GitBranch size={20} />,
          href: "/dashboard/workflows/builder",
        },
        {
          id: "hitl-approvals",
          name: "HITL Approvals",
          icon: <Users size={20} />,
          badge: 3,
          href: "/dashboard/workflows",
        },
        {
          id: "workflow-templates",
          name: "Templates",
          icon: <Layers size={20} />,
          href: "/dashboard/workflows",
        },
      ],
    },
    {
      category: "knowledge",
      title: "Knowledge",
      items: [
        {
          id: "knowledge-base",
          name: "Knowledge Base",
          icon: <Database size={20} />,
          href: "/dashboard/knowledge",
        },
        {
          id: "document-upload",
          name: "Document Upload",
          icon: <FileText size={20} />,
          href: "/dashboard/knowledge",
        },
      ],
    },
    {
      category: "analytics",
      title: "Analytics",
      items: [
        {
          id: "usage-metrics",
          name: "Usage Metrics",
          icon: <BarChart3 size={20} />,
          href: "/dashboard/analytics",
        },
        { id: "performance", name: "Performance", icon: <Gauge size={20} />, href: "/dashboard/analytics" },
      ],
    },
    {
      category: "admin",
      title: "Admin",
      items: [
        {
          id: "organization",
          name: "Organization",
          icon: <Settings size={20} />,
          href: "/dashboard/admin/organization",
        },
        { id: "team-members", name: "Team Members", icon: <Users size={20} />, href: "/dashboard/admin/team-members" },
        { id: "billing", name: "Billing", icon: <CreditCard size={20} />, href: "/dashboard/admin/billing" },
      ],
    },
  ];

  const renderNavigationItems = () => {
    return navigationItems.map((category) => (
      <div key={category.category} className="mb-2">
        {category.category !== "main" && (
          <div
            className={cn(
              "flex items-center justify-between py-2 px-3 text-sm font-medium text-muted-foreground cursor-pointer",
              collapsed && "justify-center px-2",
            )}
            onClick={() => !collapsed && toggleCategory(category.category)}
          >
            {!collapsed && <span>{category.title}</span>}
            {!collapsed && (
              <Button variant="ghost" size="sm" className="h-5 w-5 p-0">
                {expandedCategories[category.category] ? (
                  <ChevronDown size={16} />
                ) : (
                  <ChevronRight size={16} />
                )}
              </Button>
            )}
          </div>
        )}

        {(category.category === "main" ||
          expandedCategories[category.category] ||
          isCategoryActive(category) ||
          collapsed) && (
          <div className="space-y-1">
            {category.items.map((item) => {
              const isActive = isItemActive(item.href, item.id);
              return (
                <TooltipProvider
                  key={item.id}
                  delayDuration={collapsed ? 100 : 1000}
                >
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Link href={item.href}>
                        <Button
                          variant={isActive ? "secondary" : "ghost"}
                          className={cn(
                            "w-full justify-start",
                            collapsed ? "px-2" : "px-3",
                            isActive && "bg-secondary",
                          )}
                        >
                          <span className={cn("mr-2", collapsed && "mr-0")}>
                            {item.icon}
                          </span>
                          {!collapsed && <span>{item.name}</span>}
                          {!collapsed && item.badge && (
                            <Badge variant="secondary" className="ml-auto">
                              {item.badge}
                            </Badge>
                          )}
                        </Button>
                      </Link>
                    </TooltipTrigger>
                    {collapsed && (
                      <TooltipContent side="right">{item.name}</TooltipContent>
                    )}
                  </Tooltip>
                </TooltipProvider>
              );
            })}
          </div>
        )}
      </div>
    ));
  };

  const sidebarContent = (
    <div className={cn("flex h-full flex-col bg-background", className)}>
      <div className="flex h-14 items-center px-4 border-b">
        {!collapsed ? (
          <div className="flex items-center space-x-2">
            <Sparkles className="h-6 w-6 text-primary" />
            <span className="font-semibold text-lg">SynapseAI</span>
          </div>
        ) : (
          <div className="flex justify-center w-full">
            <Sparkles className="h-6 w-6 text-primary" />
          </div>
        )}
        {onToggleCollapse && (
          <Button
            variant="ghost"
            size="sm"
            className="ml-auto h-8 w-8 p-0"
            onClick={onToggleCollapse}
          >
            <Menu size={16} />
          </Button>
        )}
      </div>

      <div className={cn("p-4", collapsed && "p-2")}>
        {!collapsed ? (
          <Select
            defaultValue={selectedOrg.id}
            onValueChange={(value) => {
              const org = organizations.find((o) => o.id === value);
              if (org) setSelectedOrg(org);
            }}
          >
            <SelectTrigger className="w-full">
              <SelectValue placeholder="Select organization" />
            </SelectTrigger>
            <SelectContent>
              {organizations.map((org) => (
                <SelectItem key={org.id} value={org.id}>
                  {org.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        ) : (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button variant="outline" size="icon" className="w-full h-9">
                  <span className="font-medium text-lg">
                    {selectedOrg.name.charAt(0)}
                  </span>
                </Button>
              </TooltipTrigger>
              <TooltipContent side="right">{selectedOrg.name}</TooltipContent>
            </Tooltip>
          </TooltipProvider>
        )}
      </div>

      <ScrollArea className="flex-1 px-2">{renderNavigationItems()}</ScrollArea>

      <div className="mt-auto p-4 border-t">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <Avatar className="h-8 w-8">
              <AvatarImage src="https://api.dicebear.com/7.x/avataaars/svg?seed=John" />
              <AvatarFallback>JD</AvatarFallback>
            </Avatar>
            {!collapsed && (
              <div className="ml-2">
                <p className="text-sm font-medium">John Doe</p>
                <p className="text-xs text-muted-foreground">
                  {selectedOrg.role}
                </p>
              </div>
            )}
          </div>
          {!collapsed && (
            <div className="flex">
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button variant="ghost" size="icon" className="h-8 w-8">
                      <Bell size={16} />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>Notifications</TooltipContent>
                </Tooltip>
              </TooltipProvider>
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button variant="ghost" size="icon" className="h-8 w-8">
                      <HelpCircle size={16} />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>Help & Support</TooltipContent>
                </Tooltip>
              </TooltipProvider>
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button variant="ghost" size="icon" className="h-8 w-8">
                      <LogOut size={16} />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>Log Out</TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
          )}
          {collapsed && (
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button variant="ghost" size="icon" className="h-8 w-8">
                    <LogOut size={16} />
                  </Button>
                </TooltipTrigger>
                <TooltipContent side="right">Log Out</TooltipContent>
              </Tooltip>
            </TooltipProvider>
          )}
        </div>
      </div>
    </div>
  );

  return (
    <>
      {/* Desktop sidebar */}
      <aside
        className={cn(
          "hidden h-screen border-r md:block",
          collapsed ? "w-[70px]" : "w-[280px]",
        )}
      >
        {sidebarContent}
      </aside>

      {/* Mobile sidebar */}
      <Sheet>
        <SheetTrigger asChild>
          <Button variant="outline" size="icon" className="md:hidden">
            <Menu />
          </Button>
        </SheetTrigger>
        <SheetContent side="left" className="p-0 w-[280px]">
          {sidebarContent}
        </SheetContent>
      </Sheet>
    </>
  );
}
