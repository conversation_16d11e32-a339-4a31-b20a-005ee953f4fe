"use client";

import React, { useState } from "react";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
  CardFooter,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Tabs, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger, TabsContent } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import {
  Plus,
  Save,
  Play,
  Settings,
  ArrowRight,
  X,
  Edit,
  Copy,
  Trash2,
  ChevronDown,
  ChevronUp,
  User,
  Code,
  Zap,
  Database,
  FileText,
  MessageSquare,
} from "lucide-react";

interface WorkflowBuilderProps {
  initialWorkflow?: WorkflowData;
  onSave?: (workflow: WorkflowData) => void;
}

interface WorkflowData {
  id?: string;
  name: string;
  description: string;
  nodes: WorkflowNode[];
  connections: WorkflowConnection[];
  version: number;
}

interface WorkflowNode {
  id: string;
  type: "agent" | "tool" | "condition" | "human";
  name: string;
  position: { x: number; y: number };
  config: any;
}

interface WorkflowConnection {
  id: string;
  sourceId: string;
  targetId: string;
  label?: string;
  condition?: string;
}

const WorkflowBuilder = ({ initialWorkflow, onSave }: WorkflowBuilderProps) => {
  const [workflow, setWorkflow] = useState<WorkflowData>(
    initialWorkflow || {
      name: "New Workflow",
      description: "Workflow description",
      nodes: [],
      connections: [],
      version: 1,
    },
  );

  const [activeTab, setActiveTab] = useState("canvas");
  const [selectedNode, setSelectedNode] = useState<string | null>(null);
  const [isTestRunning, setIsTestRunning] = useState(false);
  const [showAddNodeDialog, setShowAddNodeDialog] = useState(false);
  const [showVersionHistory, setShowVersionHistory] = useState(false);

  // Mock data for available components
  const availableAgents = [
    {
      id: "agent1",
      name: "Customer Support Agent",
      description: "Handles customer inquiries and support requests",
    },
    {
      id: "agent2",
      name: "Data Analysis Agent",
      description: "Analyzes data and provides insights",
    },
    {
      id: "agent3",
      name: "Content Creation Agent",
      description: "Creates content based on provided guidelines",
    },
  ];

  const availableTools = [
    {
      id: "tool1",
      name: "Database Query",
      description: "Executes database queries",
    },
    {
      id: "tool2",
      name: "Email Sender",
      description: "Sends emails to specified recipients",
    },
    {
      id: "tool3",
      name: "API Connector",
      description: "Connects to external APIs",
    },
  ];

  // Mock version history
  const versionHistory = [
    {
      version: 3,
      date: "2023-06-15 14:32",
      author: "Jane Smith",
      changes: "Added human approval step",
    },
    {
      version: 2,
      date: "2023-06-14 10:15",
      author: "John Doe",
      changes: "Added conditional logic",
    },
    {
      version: 1,
      date: "2023-06-13 09:45",
      author: "John Doe",
      changes: "Initial workflow creation",
    },
  ];

  const handleAddNode = (
    type: "agent" | "tool" | "condition" | "human",
    name: string,
  ) => {
    const newNode: WorkflowNode = {
      id: `node-${Date.now()}`,
      type,
      name,
      position: { x: 100, y: 100 },
      config: {},
    };

    setWorkflow((prev) => ({
      ...prev,
      nodes: [...prev.nodes, newNode],
    }));
    setShowAddNodeDialog(false);
  };

  const handleDeleteNode = (nodeId: string) => {
    setWorkflow((prev) => ({
      ...prev,
      nodes: prev.nodes.filter((node) => node.id !== nodeId),
      connections: prev.connections.filter(
        (conn) => conn.sourceId !== nodeId && conn.targetId !== nodeId,
      ),
    }));
    if (selectedNode === nodeId) {
      setSelectedNode(null);
    }
  };

  const handleSaveWorkflow = () => {
    const updatedWorkflow = {
      ...workflow,
      version: workflow.version + 1,
    };
    setWorkflow(updatedWorkflow);
    if (onSave) {
      onSave(updatedWorkflow);
    }
  };

  const handleTestWorkflow = () => {
    setIsTestRunning(true);
    // Simulate test run
    setTimeout(() => {
      setIsTestRunning(false);
    }, 3000);
  };

  const handleNodeSelect = (nodeId: string) => {
    setSelectedNode(nodeId === selectedNode ? null : nodeId);
  };

  return (
    <div className="flex flex-col h-full bg-background">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b">
        <div className="flex items-center gap-4">
          <div>
            <h1 className="text-2xl font-bold">{workflow.name}</h1>
            <p className="text-sm text-muted-foreground">
              Version {workflow.version}
            </p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={() => setShowVersionHistory(true)}>
            History
          </Button>
          <Button
            variant="outline"
            onClick={handleTestWorkflow}
            disabled={isTestRunning || workflow.nodes.length === 0}
          >
            {isTestRunning ? "Running..." : "Test"}
            {!isTestRunning && <Play className="ml-2 h-4 w-4" />}
          </Button>
          <Button onClick={handleSaveWorkflow}>
            Save
            <Save className="ml-2 h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex flex-1 overflow-hidden">
        {/* Left Sidebar - Components */}
        <div className="w-64 border-r p-4 flex flex-col">
          <h2 className="font-semibold mb-4">Components</h2>
          <div className="space-y-4">
            <div>
              <h3 className="text-sm font-medium mb-2 flex items-center">
                <MessageSquare className="h-4 w-4 mr-2" />
                Agents
              </h3>
              <ScrollArea className="h-32">
                {availableAgents.map((agent) => (
                  <div
                    key={agent.id}
                    className="p-2 text-sm border rounded-md mb-2 cursor-pointer hover:bg-accent"
                    onClick={() => handleAddNode("agent", agent.name)}
                  >
                    {agent.name}
                  </div>
                ))}
              </ScrollArea>
            </div>

            <div>
              <h3 className="text-sm font-medium mb-2 flex items-center">
                <Zap className="h-4 w-4 mr-2" />
                Tools
              </h3>
              <ScrollArea className="h-32">
                {availableTools.map((tool) => (
                  <div
                    key={tool.id}
                    className="p-2 text-sm border rounded-md mb-2 cursor-pointer hover:bg-accent"
                    onClick={() => handleAddNode("tool", tool.name)}
                  >
                    {tool.name}
                  </div>
                ))}
              </ScrollArea>
            </div>

            <div className="space-y-2">
              <h3 className="text-sm font-medium mb-2">Logic</h3>
              <div
                className="p-2 text-sm border rounded-md cursor-pointer hover:bg-accent flex items-center"
                onClick={() => handleAddNode("condition", "Condition")}
              >
                <Code className="h-4 w-4 mr-2" />
                Condition
              </div>
              <div
                className="p-2 text-sm border rounded-md cursor-pointer hover:bg-accent flex items-center"
                onClick={() => handleAddNode("human", "Human Approval")}
              >
                <User className="h-4 w-4 mr-2" />
                Human Approval
              </div>
            </div>
          </div>
        </div>

        {/* Main Canvas */}
        <div className="flex-1 overflow-auto">
          <Tabs
            value={activeTab}
            onValueChange={setActiveTab}
            className="h-full"
          >
            <div className="border-b px-4">
              <TabsList>
                <TabsTrigger value="canvas">Canvas</TabsTrigger>
                <TabsTrigger value="code">Code View</TabsTrigger>
                <TabsTrigger value="test">Test Results</TabsTrigger>
              </TabsList>
            </div>

            <TabsContent value="canvas" className="p-4 h-full">
              <div className="border-2 border-dashed rounded-lg h-full p-4 relative bg-slate-50">
                {workflow.nodes.length === 0 ? (
                  <div className="absolute inset-0 flex items-center justify-center text-muted-foreground">
                    Drag components from the sidebar to build your workflow
                  </div>
                ) : (
                  <div className="relative h-full">
                    {/* Render workflow nodes */}
                    {workflow.nodes.map((node) => (
                      <div
                        key={node.id}
                        className={`absolute p-4 rounded-md shadow-md cursor-move ${selectedNode === node.id ? "ring-2 ring-primary" : ""}`}
                        style={{
                          left: `${node.position.x}px`,
                          top: `${node.position.y}px`,
                          backgroundColor: getNodeColor(node.type),
                        }}
                        onClick={() => handleNodeSelect(node.id)}
                      >
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center">
                            {getNodeIcon(node.type)}
                            <span className="ml-2 font-medium">
                              {node.name}
                            </span>
                          </div>
                          <div className="flex items-center gap-1">
                            <button
                              className="p-1 hover:bg-white/20 rounded"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleDeleteNode(node.id);
                              }}
                            >
                              <X className="h-4 w-4" />
                            </button>
                          </div>
                        </div>
                        <Badge variant="outline" className="bg-white/30">
                          {node.type}
                        </Badge>
                      </div>
                    ))}

                    {/* Render connections (simplified) */}
                    {workflow.connections.map((conn) => (
                      <div
                        key={conn.id}
                        className="absolute bg-primary h-0.5"
                        style={{
                          // This is a simplified representation - in a real app you'd calculate actual path
                          left: "200px",
                          top: "150px",
                          width: "100px",
                          transform: "rotate(45deg)",
                        }}
                      />
                    ))}
                  </div>
                )}
              </div>
            </TabsContent>

            <TabsContent value="code" className="p-4">
              <Card>
                <CardHeader>
                  <CardTitle>Workflow Code</CardTitle>
                  <CardDescription>
                    JSON representation of your workflow
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <pre className="p-4 bg-slate-100 rounded-md overflow-auto max-h-96">
                    {JSON.stringify(workflow, null, 2)}
                  </pre>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="test" className="p-4">
              <Card>
                <CardHeader>
                  <CardTitle>Test Results</CardTitle>
                  <CardDescription>
                    Results from your workflow test runs
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {isTestRunning ? (
                    <div className="flex items-center justify-center p-8">
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                      <span className="ml-3">Running test...</span>
                    </div>
                  ) : workflow.nodes.length === 0 ? (
                    <div className="text-center p-8 text-muted-foreground">
                      Add components to your workflow and run a test to see
                      results
                    </div>
                  ) : (
                    <div className="space-y-4">
                      <div className="p-4 border rounded-md">
                        <h3 className="font-medium">Test Run #1</h3>
                        <p className="text-sm text-muted-foreground mb-2">
                          Completed in 1.2s
                        </p>
                        <div className="space-y-2">
                          <div className="flex items-center text-sm">
                            <Badge
                              variant="outline"
                              className="mr-2 bg-green-100"
                            >
                              Success
                            </Badge>
                            <span>Customer Support Agent processed input</span>
                          </div>
                          <div className="flex items-center text-sm">
                            <Badge
                              variant="outline"
                              className="mr-2 bg-green-100"
                            >
                              Success
                            </Badge>
                            <span>Database Query executed</span>
                          </div>
                          <div className="flex items-center text-sm">
                            <Badge
                              variant="outline"
                              className="mr-2 bg-blue-100"
                            >
                              Waiting
                            </Badge>
                            <span>Human Approval pending</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>

        {/* Right Sidebar - Properties */}
        <div className="w-80 border-l p-4">
          <h2 className="font-semibold mb-4">Properties</h2>
          {selectedNode ? (
            <NodeProperties
              node={workflow.nodes.find((n) => n.id === selectedNode)!}
              onUpdate={(updatedNode) => {
                setWorkflow((prev) => ({
                  ...prev,
                  nodes: prev.nodes.map((n) =>
                    n.id === selectedNode ? updatedNode : n,
                  ),
                }));
              }}
            />
          ) : (
            <WorkflowProperties
              workflow={workflow}
              onUpdate={(updates) => {
                setWorkflow((prev) => ({ ...prev, ...updates }));
              }}
            />
          )}
        </div>
      </div>

      {/* Version History Dialog */}
      <Dialog open={showVersionHistory} onOpenChange={setShowVersionHistory}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Version History</DialogTitle>
            <DialogDescription>
              View and restore previous versions of this workflow.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 max-h-96 overflow-auto">
            {versionHistory.map((version) => (
              <div key={version.version} className="p-4 border rounded-md">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="font-medium">Version {version.version}</h3>
                    <p className="text-sm text-muted-foreground">
                      {version.date} by {version.author}
                    </p>
                  </div>
                  <Button variant="outline" size="sm">
                    Restore
                  </Button>
                </div>
                <p className="text-sm mt-2">{version.changes}</p>
              </div>
            ))}
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowVersionHistory(false)}
            >
              Close
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

const NodeProperties = ({
  node,
  onUpdate,
}: {
  node: WorkflowNode;
  onUpdate: (node: WorkflowNode) => void;
}) => {
  const [nodeData, setNodeData] = useState(node);

  const handleChange = (field: string, value: any) => {
    const updated = { ...nodeData, [field]: value };
    setNodeData(updated);
    onUpdate(updated);
  };

  const handleConfigChange = (field: string, value: any) => {
    const updatedConfig = { ...nodeData.config, [field]: value };
    const updated = { ...nodeData, config: updatedConfig };
    setNodeData(updated);
    onUpdate(updated);
  };

  return (
    <div className="space-y-4">
      <div>
        <Label htmlFor="node-name">Name</Label>
        <Input
          id="node-name"
          value={nodeData.name}
          onChange={(e) => handleChange("name", e.target.value)}
          className="mt-1"
        />
      </div>

      <div>
        <Label>Type</Label>
        <div className="mt-1 flex items-center">
          {getNodeIcon(node.type)}
          <span className="ml-2 capitalize">{node.type}</span>
        </div>
      </div>

      <Separator />

      {node.type === "agent" && (
        <div className="space-y-4">
          <div>
            <Label htmlFor="agent-model">AI Model</Label>
            <Select
              value={nodeData.config?.model || "gpt-4"}
              onValueChange={(value) => handleConfigChange("model", value)}
            >
              <SelectTrigger id="agent-model" className="mt-1">
                <SelectValue placeholder="Select model" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="gpt-4">GPT-4</SelectItem>
                <SelectItem value="gpt-3.5-turbo">GPT-3.5 Turbo</SelectItem>
                <SelectItem value="claude-3">Claude 3</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label htmlFor="agent-template">Prompt Template</Label>
            <Select
              value={nodeData.config?.templateId || ""}
              onValueChange={(value) => handleConfigChange("templateId", value)}
            >
              <SelectTrigger id="agent-template" className="mt-1">
                <SelectValue placeholder="Select template" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="template1">Customer Support</SelectItem>
                <SelectItem value="template2">Data Analysis</SelectItem>
                <SelectItem value="template3">Content Creation</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      )}

      {node.type === "tool" && (
        <div className="space-y-4">
          <div>
            <Label htmlFor="tool-api">API Endpoint</Label>
            <Input
              id="tool-api"
              value={nodeData.config?.apiEndpoint || ""}
              onChange={(e) =>
                handleConfigChange("apiEndpoint", e.target.value)
              }
              className="mt-1"
              placeholder="https://api.example.com/endpoint"
            />
          </div>

          <div>
            <Label htmlFor="tool-method">Method</Label>
            <Select
              value={nodeData.config?.method || "GET"}
              onValueChange={(value) => handleConfigChange("method", value)}
            >
              <SelectTrigger id="tool-method" className="mt-1">
                <SelectValue placeholder="Select method" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="GET">GET</SelectItem>
                <SelectItem value="POST">POST</SelectItem>
                <SelectItem value="PUT">PUT</SelectItem>
                <SelectItem value="DELETE">DELETE</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      )}

      {node.type === "condition" && (
        <div className="space-y-4">
          <div>
            <Label htmlFor="condition-expression">Condition Expression</Label>
            <Input
              id="condition-expression"
              value={nodeData.config?.expression || ""}
              onChange={(e) => handleConfigChange("expression", e.target.value)}
              className="mt-1"
              placeholder="result.status === 'success'"
            />
          </div>

          <div>
            <Label>Branches</Label>
            <div className="mt-2 space-y-2">
              <div className="flex items-center justify-between p-2 border rounded-md">
                <span>True</span>
                <Button variant="ghost" size="sm">
                  <Settings className="h-4 w-4" />
                </Button>
              </div>
              <div className="flex items-center justify-between p-2 border rounded-md">
                <span>False</span>
                <Button variant="ghost" size="sm">
                  <Settings className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}

      {node.type === "human" && (
        <div className="space-y-4">
          <div>
            <Label htmlFor="human-role">Approver Role</Label>
            <Select
              value={nodeData.config?.approverRole || "manager"}
              onValueChange={(value) =>
                handleConfigChange("approverRole", value)
              }
            >
              <SelectTrigger id="human-role" className="mt-1">
                <SelectValue placeholder="Select role" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="manager">Manager</SelectItem>
                <SelectItem value="admin">Administrator</SelectItem>
                <SelectItem value="support">Support Agent</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label htmlFor="timeout">Timeout (hours)</Label>
            <Input
              id="timeout"
              type="number"
              value={nodeData.config?.timeoutHours || "24"}
              onChange={(e) =>
                handleConfigChange("timeoutHours", e.target.value)
              }
              className="mt-1"
            />
          </div>

          <div className="flex items-center space-x-2">
            <Checkbox
              id="escalate"
              checked={nodeData.config?.escalateOnTimeout || false}
              onCheckedChange={(checked) =>
                handleConfigChange("escalateOnTimeout", checked)
              }
            />
            <Label htmlFor="escalate">Escalate on timeout</Label>
          </div>
        </div>
      )}
    </div>
  );
};

const WorkflowProperties = ({
  workflow,
  onUpdate,
}: {
  workflow: WorkflowData;
  onUpdate: (updates: Partial<WorkflowData>) => void;
}) => {
  return (
    <div className="space-y-4">
      <div>
        <Label htmlFor="workflow-name">Name</Label>
        <Input
          id="workflow-name"
          value={workflow.name}
          onChange={(e) => onUpdate({ name: e.target.value })}
          className="mt-1"
        />
      </div>

      <div>
        <Label htmlFor="workflow-description">Description</Label>
        <Input
          id="workflow-description"
          value={workflow.description}
          onChange={(e) => onUpdate({ description: e.target.value })}
          className="mt-1"
        />
      </div>

      <div>
        <Label>Statistics</Label>
        <div className="mt-2 space-y-2 text-sm">
          <div className="flex justify-between">
            <span className="text-muted-foreground">Nodes</span>
            <span>{workflow.nodes.length}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-muted-foreground">Connections</span>
            <span>{workflow.connections.length}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-muted-foreground">Version</span>
            <span>{workflow.version}</span>
          </div>
        </div>
      </div>

      <Separator />

      <div>
        <h3 className="text-sm font-medium mb-2">Advanced Settings</h3>
        <div className="space-y-2">
          <div className="flex items-center space-x-2">
            <Checkbox id="error-handling" />
            <Label htmlFor="error-handling">Enable error handling</Label>
          </div>
          <div className="flex items-center space-x-2">
            <Checkbox id="logging" />
            <Label htmlFor="logging">Enable detailed logging</Label>
          </div>
          <div className="flex items-center space-x-2">
            <Checkbox id="timeout" />
            <Label htmlFor="timeout">Set global timeout</Label>
          </div>
        </div>
      </div>

      <AlertDialog>
        <AlertDialogTrigger asChild>
          <Button variant="destructive" className="w-full mt-4">
            <Trash2 className="h-4 w-4 mr-2" />
            Delete Workflow
          </Button>
        </AlertDialogTrigger>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the
              workflow and all of its data.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction>Delete</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};

// Helper functions
const getNodeColor = (type: string): string => {
  switch (type) {
    case "agent":
      return "#e0f2fe"; // Light blue
    case "tool":
      return "#dcfce7"; // Light green
    case "condition":
      return "#fef3c7"; // Light yellow
    case "human":
      return "#fce7f3"; // Light pink
    default:
      return "#f3f4f6"; // Light gray
  }
};

const getNodeIcon = (type: string) => {
  switch (type) {
    case "agent":
      return <MessageSquare className="h-4 w-4" />;
    case "tool":
      return <Zap className="h-4 w-4" />;
    case "condition":
      return <Code className="h-4 w-4" />;
    case "human":
      return <User className="h-4 w-4" />;
    default:
      return <div className="h-4 w-4" />;
  }
};

export default WorkflowBuilder;
