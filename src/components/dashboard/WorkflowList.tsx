"use client";

import React, { useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Search,
  Plus,
  Play,
  Pause,
  Edit,
  Copy,
  Trash2,
  <PERSON><PERSON><PERSON>zon<PERSON>,
  Filter,
  GitBranch,
  Clock,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Users,
  Calendar,
  Activity,
} from "lucide-react";

interface WorkflowListProps {
  className?: string;
}

interface Workflow {
  id: string;
  name: string;
  description: string;
  status: "active" | "paused" | "draft" | "error";
  lastRun: string;
  totalRuns: number;
  successRate: number;
  avgDuration: string;
  createdBy: string;
  createdAt: string;
  tags: string[];
  nodes: number;
  version: number;
}

export default function WorkflowList({ className = "" }: WorkflowListProps) {
  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [sortBy, setSortBy] = useState("lastRun");
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc");
  const [selectedWorkflows, setSelectedWorkflows] = useState<string[]>([]);
  const [showCreateDialog, setShowCreateDialog] = useState(false);

  const workflows: Workflow[] = [
    {
      id: "wf_1",
      name: "Customer Onboarding",
      description:
        "Automated customer onboarding process with welcome emails and account setup",
      status: "active",
      lastRun: "2024-01-15T14:30:00Z",
      totalRuns: 156,
      successRate: 94.2,
      avgDuration: "2.3s",
      createdBy: "John Doe",
      createdAt: "2023-12-01T10:00:00Z",
      tags: ["onboarding", "email", "automation"],
      nodes: 8,
      version: 3,
    },
    {
      id: "wf_2",
      name: "Lead Qualification",
      description:
        "Qualify leads based on company size, budget, and requirements",
      status: "active",
      lastRun: "2024-01-15T12:15:00Z",
      totalRuns: 89,
      successRate: 87.6,
      avgDuration: "1.8s",
      createdBy: "Jane Smith",
      createdAt: "2023-11-15T09:30:00Z",
      tags: ["sales", "qualification", "crm"],
      nodes: 6,
      version: 2,
    },
    {
      id: "wf_3",
      name: "Support Ticket Routing",
      description:
        "Automatically route support tickets to appropriate team members",
      status: "paused",
      lastRun: "2024-01-14T16:45:00Z",
      totalRuns: 234,
      successRate: 96.8,
      avgDuration: "0.9s",
      createdBy: "Mike Johnson",
      createdAt: "2023-10-20T14:00:00Z",
      tags: ["support", "routing", "tickets"],
      nodes: 4,
      version: 1,
    },
    {
      id: "wf_4",
      name: "Invoice Processing",
      description: "Process invoices and update accounting system",
      status: "error",
      lastRun: "2024-01-15T08:00:00Z",
      totalRuns: 45,
      successRate: 73.3,
      avgDuration: "4.1s",
      createdBy: "Sarah Wilson",
      createdAt: "2024-01-01T11:00:00Z",
      tags: ["finance", "invoices", "accounting"],
      nodes: 12,
      version: 1,
    },
    {
      id: "wf_5",
      name: "Content Moderation",
      description: "Moderate user-generated content using AI and human review",
      status: "draft",
      lastRun: "Never",
      totalRuns: 0,
      successRate: 0,
      avgDuration: "N/A",
      createdBy: "Alex Brown",
      createdAt: "2024-01-10T13:30:00Z",
      tags: ["content", "moderation", "ai"],
      nodes: 7,
      version: 1,
    },
  ];

  const getStatusBadge = (status: string) => {
    const variants = {
      active: "default",
      paused: "secondary",
      draft: "outline",
      error: "destructive",
    } as const;

    const icons = {
      active: <CheckCircle className="mr-1 h-3 w-3" />,
      paused: <Pause className="mr-1 h-3 w-3" />,
      draft: <Clock className="mr-1 h-3 w-3" />,
      error: <XCircle className="mr-1 h-3 w-3" />,
    };

    return (
      <Badge variant={variants[status as keyof typeof variants]}>
        {icons[status as keyof typeof icons]}
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    );
  };

  const getSuccessRateColor = (rate: number) => {
    if (rate >= 90) return "text-green-600";
    if (rate >= 75) return "text-yellow-600";
    return "text-red-600";
  };

  const filteredWorkflows = workflows
    .filter((workflow) => {
      const matchesSearch =
        workflow.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        workflow.description
          .toLowerCase()
          .includes(searchQuery.toLowerCase()) ||
        workflow.tags.some((tag) =>
          tag.toLowerCase().includes(searchQuery.toLowerCase()),
        );
      const matchesStatus =
        statusFilter === "all" || workflow.status === statusFilter;
      return matchesSearch && matchesStatus;
    })
    .sort((a, b) => {
      let aValue: any, bValue: any;
      switch (sortBy) {
        case "name":
          aValue = a.name;
          bValue = b.name;
          break;
        case "lastRun":
          aValue = new Date(a.lastRun === "Never" ? 0 : a.lastRun);
          bValue = new Date(b.lastRun === "Never" ? 0 : b.lastRun);
          break;
        case "totalRuns":
          aValue = a.totalRuns;
          bValue = b.totalRuns;
          break;
        case "successRate":
          aValue = a.successRate;
          bValue = b.successRate;
          break;
        default:
          return 0;
      }

      if (sortOrder === "asc") {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });

  const handleSelectWorkflow = (workflowId: string) => {
    setSelectedWorkflows((prev) =>
      prev.includes(workflowId)
        ? prev.filter((id) => id !== workflowId)
        : [...prev, workflowId],
    );
  };

  const handleSelectAll = () => {
    if (selectedWorkflows.length === filteredWorkflows.length) {
      setSelectedWorkflows([]);
    } else {
      setSelectedWorkflows(filteredWorkflows.map((w) => w.id));
    }
  };

  const handleBulkAction = (action: string) => {
    console.log(`Performing ${action} on workflows:`, selectedWorkflows);
    // Implement bulk actions here
    setSelectedWorkflows([]);
  };

  return (
    <div className={`bg-background min-h-screen p-6 ${className}`}>
      <div className="max-w-7xl mx-auto">
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Workflows</h1>
            <p className="text-muted-foreground">
              Manage and monitor your automated workflows
            </p>
          </div>
          <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="mr-2 h-4 w-4" />
                Create Workflow
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Create New Workflow</DialogTitle>
                <DialogDescription>
                  Start building a new automated workflow
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <div className="space-y-2">
                  <label
                    htmlFor="workflow-name"
                    className="text-sm font-medium"
                  >
                    Workflow Name
                  </label>
                  <Input id="workflow-name" placeholder="Enter workflow name" />
                </div>
                <div className="space-y-2">
                  <label
                    htmlFor="workflow-description"
                    className="text-sm font-medium"
                  >
                    Description
                  </label>
                  <Input
                    id="workflow-description"
                    placeholder="Describe what this workflow does"
                  />
                </div>
              </div>
              <DialogFooter>
                <Button
                  variant="outline"
                  onClick={() => setShowCreateDialog(false)}
                >
                  Cancel
                </Button>
                <Button onClick={() => setShowCreateDialog(false)}>
                  Create Workflow
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>

        {/* Stats Cards */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4 mb-6">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">
                Total Workflows
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{workflows.length}</div>
              <div className="flex items-center text-xs text-muted-foreground">
                <GitBranch className="mr-1 h-3 w-3" />
                {workflows.filter((w) => w.status === "active").length} active
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">
                Total Executions
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {workflows.reduce((sum, w) => sum + w.totalRuns, 0)}
              </div>
              <div className="flex items-center text-xs text-muted-foreground">
                <Activity className="mr-1 h-3 w-3" />
                This month
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">
                Average Success Rate
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {Math.round(
                  workflows.reduce((sum, w) => sum + w.successRate, 0) /
                    workflows.length,
                )}
                %
              </div>
              <div className="flex items-center text-xs text-muted-foreground">
                <CheckCircle className="mr-1 h-3 w-3" />
                Across all workflows
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">
                Active Users
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {new Set(workflows.map((w) => w.createdBy)).size}
              </div>
              <div className="flex items-center text-xs text-muted-foreground">
                <Users className="mr-1 h-3 w-3" />
                Creating workflows
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters and Search */}
        <Card className="mb-6">
          <CardContent className="pt-6">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search workflows..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-48">
                  <Filter className="mr-2 h-4 w-4" />
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="paused">Paused</SelectItem>
                  <SelectItem value="draft">Draft</SelectItem>
                  <SelectItem value="error">Error</SelectItem>
                </SelectContent>
              </Select>
              <Select value={sortBy} onValueChange={setSortBy}>
                <SelectTrigger className="w-48">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="lastRun">Last Run</SelectItem>
                  <SelectItem value="name">Name</SelectItem>
                  <SelectItem value="totalRuns">Total Runs</SelectItem>
                  <SelectItem value="successRate">Success Rate</SelectItem>
                </SelectContent>
              </Select>
            </div>
            {selectedWorkflows.length > 0 && (
              <div className="flex items-center gap-2 mt-4 p-3 bg-muted rounded-lg">
                <span className="text-sm font-medium">
                  {selectedWorkflows.length} workflow
                  {selectedWorkflows.length > 1 ? "s" : ""} selected
                </span>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => handleBulkAction("pause")}
                >
                  <Pause className="mr-1 h-3 w-3" />
                  Pause
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => handleBulkAction("activate")}
                >
                  <Play className="mr-1 h-3 w-3" />
                  Activate
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => handleBulkAction("delete")}
                >
                  <Trash2 className="mr-1 h-3 w-3" />
                  Delete
                </Button>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Workflows Table */}
        <Card>
          <CardHeader>
            <CardTitle>Workflows ({filteredWorkflows.length})</CardTitle>
            <CardDescription>
              Manage your automated workflows and monitor their performance
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-12">
                    <input
                      type="checkbox"
                      checked={
                        selectedWorkflows.length === filteredWorkflows.length &&
                        filteredWorkflows.length > 0
                      }
                      onChange={handleSelectAll}
                    />
                  </TableHead>
                  <TableHead>Workflow</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Last Run</TableHead>
                  <TableHead>Runs</TableHead>
                  <TableHead>Success Rate</TableHead>
                  <TableHead>Avg Duration</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredWorkflows.map((workflow) => (
                  <TableRow key={workflow.id}>
                    <TableCell>
                      <input
                        type="checkbox"
                        checked={selectedWorkflows.includes(workflow.id)}
                        onChange={() => handleSelectWorkflow(workflow.id)}
                      />
                    </TableCell>
                    <TableCell>
                      <div className="space-y-1">
                        <div className="font-medium">{workflow.name}</div>
                        <div className="text-sm text-muted-foreground">
                          {workflow.description}
                        </div>
                        <div className="flex gap-1">
                          {workflow.tags.map((tag) => (
                            <Badge
                              key={tag}
                              variant="outline"
                              className="text-xs"
                            >
                              {tag}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>{getStatusBadge(workflow.status)}</TableCell>
                    <TableCell className="text-sm text-muted-foreground">
                      {workflow.lastRun === "Never" ? (
                        "Never"
                      ) : (
                        <div>
                          <div>
                            {new Date(workflow.lastRun).toLocaleDateString()}
                          </div>
                          <div className="text-xs">
                            {new Date(workflow.lastRun).toLocaleTimeString()}
                          </div>
                        </div>
                      )}
                    </TableCell>
                    <TableCell>
                      <div className="font-medium">{workflow.totalRuns}</div>
                      <div className="text-xs text-muted-foreground">
                        {workflow.nodes} nodes
                      </div>
                    </TableCell>
                    <TableCell>
                      <div
                        className={`font-medium ${getSuccessRateColor(workflow.successRate)}`}
                      >
                        {workflow.successRate}%
                      </div>
                    </TableCell>
                    <TableCell className="text-sm">
                      {workflow.avgDuration}
                    </TableCell>
                    <TableCell>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuLabel>Actions</DropdownMenuLabel>
                          <DropdownMenuItem>
                            <Edit className="mr-2 h-4 w-4" />
                            Edit Workflow
                          </DropdownMenuItem>
                          <DropdownMenuItem>
                            <Play className="mr-2 h-4 w-4" />
                            Run Now
                          </DropdownMenuItem>
                          <DropdownMenuItem>
                            <Copy className="mr-2 h-4 w-4" />
                            Duplicate
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem>
                            <Activity className="mr-2 h-4 w-4" />
                            View Logs
                          </DropdownMenuItem>
                          <DropdownMenuItem>
                            <Calendar className="mr-2 h-4 w-4" />
                            Schedule
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem className="text-red-600">
                            <Trash2 className="mr-2 h-4 w-4" />
                            Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
