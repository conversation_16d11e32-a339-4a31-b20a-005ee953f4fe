"use client";

import React, { useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Tabs, TabsContent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  AlertCircle,
  Check,
  ChevronRight,
  Code,
  Copy,
  Play,
  Plus,
  Save,
  Settings,
  Sparkles,
  Trash2,
  Wand2,
} from "lucide-react";
import { <PERSON><PERSON>, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { toast } from "@/components/ui/use-toast";
import { useWebSocket } from "@/lib/websocket";

interface AgentBuilderProps {
  initialAgentData?: AgentData;
  onSave?: (agentData: AgentData) => void;
  onTest?: (agentData: AgentData) => void;
  onDeploy?: (agentData: AgentData) => void;
}

interface AgentData {
  id?: string;
  name: string;
  description: string;
  provider: string;
  model: string;
  promptTemplate: string;
  parameters: Parameter[];
  systemPrompt: string;
  temperature: number;
  maxTokens: number;
  isPublic: boolean;
}

interface Parameter {
  name: string;
  type: string;
  description: string;
  required: boolean;
  defaultValue?: string;
}

const defaultAgentData: AgentData = {
  name: "New Agent",
  description: "A helpful AI assistant",
  provider: "openai",
  model: "gpt-4o",
  promptTemplate:
    "You are a helpful assistant. Answer the following question: {{question}}",
  parameters: [
    {
      name: "question",
      type: "string",
      description: "The question to answer",
      required: true,
    },
  ],
  systemPrompt:
    "You are a helpful AI assistant that provides accurate and concise information.",
  temperature: 0.7,
  maxTokens: 1000,
  isPublic: false,
};

const AgentBuilder: React.FC<AgentBuilderProps> = ({
  initialAgentData = defaultAgentData,
  onSave = () => {},
  onTest = () => {},
  onDeploy = () => {},
}) => {
  const { send } = useWebSocket();
  const [agentData, setAgentData] = useState<AgentData>(initialAgentData);
  const [activeTab, setActiveTab] = useState("config");
  const [testInput, setTestInput] = useState<Record<string, string>>({
    question: "What is artificial intelligence?",
  });
  const [testResponse, setTestResponse] = useState<string>("");
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [savedTemplates, setSavedTemplates] = useState([
    {
      id: "1",
      name: "General Assistant",
      description: "Basic helpful assistant template",
    },
    {
      id: "2",
      name: "Customer Support",
      description: "Template for handling customer inquiries",
    },
    {
      id: "3",
      name: "Code Helper",
      description: "Template for programming assistance",
    },
  ]);

  const handleInputChange = (field: keyof AgentData, value: any) => {
    setAgentData({ ...agentData, [field]: value });
  };

  const handleParameterChange = (
    index: number,
    field: keyof Parameter,
    value: any,
  ) => {
    const updatedParameters = [...agentData.parameters];
    updatedParameters[index] = { ...updatedParameters[index], [field]: value };
    setAgentData({ ...agentData, parameters: updatedParameters });
  };

  const addParameter = () => {
    setAgentData({
      ...agentData,
      parameters: [
        ...agentData.parameters,
        { name: "", type: "string", description: "", required: false },
      ],
    });
  };

  const removeParameter = (index: number) => {
    const updatedParameters = [...agentData.parameters];
    updatedParameters.splice(index, 1);
    setAgentData({ ...agentData, parameters: updatedParameters });
  };

  const handleTestInputChange = (paramName: string, value: string) => {
    setTestInput({ ...testInput, [paramName]: value });
  };

  const handleTest = () => {
    setIsLoading(true);
    setTestResponse("");

    // Simulate API call with a timeout
    setTimeout(() => {
      const simulatedResponse =
        "Artificial Intelligence (AI) refers to computer systems designed to perform tasks that typically require human intelligence. These tasks include learning, reasoning, problem-solving, perception, and language understanding. AI can be categorized into narrow AI (designed for specific tasks) and general AI (with broader human-like capabilities). Modern AI applications include virtual assistants, recommendation systems, autonomous vehicles, and advanced data analysis tools. The field continues to evolve rapidly with developments in machine learning, neural networks, and natural language processing.";
      setTestResponse(simulatedResponse);
      setIsLoading(false);
    }, 2000);

    onTest(agentData);
  };

  const handleSave = () => {
    onSave(agentData);
    // Show success notification (would be implemented with a toast system)
  };

  const handleDeploy = () => {
    onDeploy(agentData);
    send("agent.deploy", {
      agentId: agentData.id || Date.now().toString(),
      agentData,
    });
    toast({
      title: "Agent Deployed",
      description: `Agent "${agentData.name}" is being deployed.`,
    });
  };

  const applyTemplate = (templateId: string) => {
    // In a real implementation, this would fetch the template data
    // For now, we'll just simulate applying a template
    if (templateId === "1") {
      setAgentData({
        ...agentData,
        systemPrompt:
          "You are a helpful, friendly AI assistant that provides accurate and concise information.",
        promptTemplate: "Please answer the following question: {{question}}",
      });
    } else if (templateId === "2") {
      setAgentData({
        ...agentData,
        systemPrompt:
          "You are a customer support representative for SynapseAI. Be helpful, friendly, and solution-oriented.",
        promptTemplate:
          "A customer has the following question: {{question}}\n\nPlease provide a helpful response.",
      });
    } else if (templateId === "3") {
      setAgentData({
        ...agentData,
        systemPrompt:
          "You are a coding assistant with expertise in multiple programming languages. Provide clear, efficient code examples and explanations.",
        promptTemplate:
          "I need help with the following coding task: {{question}}\n\nPlease provide code and explanation.",
      });
    }
  };

  return (
    <div className="w-full h-full flex flex-col">
      <div className="flex justify-between items-center p-6 border-b">
        <div>
          <h1 className="text-2xl font-bold">Agent Builder</h1>
          <p className="text-muted-foreground">
            Create and configure AI agents with reusable prompt templates
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={handleSave}>
            <Save className="mr-2 h-4 w-4" />
            Save
          </Button>
          <Button variant="outline" onClick={handleTest}>
            <Play className="mr-2 h-4 w-4" />
            Test
          </Button>
          <Button onClick={handleDeploy}>
            <Sparkles className="mr-2 h-4 w-4" />
            Deploy
          </Button>
        </div>
      </div>

      <div className="flex flex-1 overflow-hidden">
        <div className="w-2/3 p-4 overflow-auto">
          <Tabs
            value={activeTab}
            onValueChange={setActiveTab}
            className="w-full"
          >
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="config">Configuration</TabsTrigger>
              <TabsTrigger value="prompt">Prompt Template</TabsTrigger>
              <TabsTrigger value="params">Parameters</TabsTrigger>
            </TabsList>

            <TabsContent value="config" className="space-y-4 mt-4">
              <Card>
                <CardHeader>
                  <CardTitle>Basic Configuration</CardTitle>
                  <CardDescription>
                    Configure the basic settings for your agent
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="name">Agent Name</Label>
                      <Input
                        id="name"
                        value={agentData.name}
                        onChange={(e) =>
                          handleInputChange("name", e.target.value)
                        }
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="provider">Provider</Label>
                      <Select
                        value={agentData.provider}
                        onValueChange={(value) =>
                          handleInputChange("provider", value)
                        }
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select provider" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="openai">OpenAI</SelectItem>
                          <SelectItem value="anthropic">Anthropic</SelectItem>
                          <SelectItem value="google">Google AI</SelectItem>
                          <SelectItem value="mistral">Mistral AI</SelectItem>
                          <SelectItem value="groq">Groq</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="description">Description</Label>
                    <Textarea
                      id="description"
                      value={agentData.description}
                      onChange={(e) =>
                        handleInputChange("description", e.target.value)
                      }
                      rows={2}
                    />
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="model">Model</Label>
                      <Select
                        value={agentData.model}
                        onValueChange={(value) =>
                          handleInputChange("model", value)
                        }
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select model" />
                        </SelectTrigger>
                        <SelectContent>
                          {agentData.provider === "openai" && (
                            <>
                              <SelectItem value="gpt-4o">GPT-4o</SelectItem>
                              <SelectItem value="gpt-4-turbo">
                                GPT-4 Turbo
                              </SelectItem>
                              <SelectItem value="gpt-3.5-turbo">
                                GPT-3.5 Turbo
                              </SelectItem>
                            </>
                          )}
                          {agentData.provider === "anthropic" && (
                            <>
                              <SelectItem value="claude-3-opus">
                                Claude 3 Opus
                              </SelectItem>
                              <SelectItem value="claude-3-sonnet">
                                Claude 3 Sonnet
                              </SelectItem>
                              <SelectItem value="claude-3-haiku">
                                Claude 3 Haiku
                              </SelectItem>
                            </>
                          )}
                          {agentData.provider === "google" && (
                            <>
                              <SelectItem value="gemini-pro">
                                Gemini Pro
                              </SelectItem>
                              <SelectItem value="gemini-ultra">
                                Gemini Ultra
                              </SelectItem>
                            </>
                          )}
                          {agentData.provider === "mistral" && (
                            <>
                              <SelectItem value="mistral-large">
                                Mistral Large
                              </SelectItem>
                              <SelectItem value="mistral-medium">
                                Mistral Medium
                              </SelectItem>
                              <SelectItem value="mistral-small">
                                Mistral Small
                              </SelectItem>
                            </>
                          )}
                          {agentData.provider === "groq" && (
                            <>
                              <SelectItem value="llama3-70b">
                                LLaMA 3 70B
                              </SelectItem>
                              <SelectItem value="llama3-8b">
                                LLaMA 3 8B
                              </SelectItem>
                            </>
                          )}
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="visibility">Visibility</Label>
                      <div className="flex items-center space-x-2 pt-2">
                        <Switch
                          id="visibility"
                          checked={agentData.isPublic}
                          onCheckedChange={(checked) =>
                            handleInputChange("isPublic", checked)
                          }
                        />
                        <Label htmlFor="visibility">
                          {agentData.isPublic ? "Public" : "Private"}
                        </Label>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Advanced Settings</CardTitle>
                  <CardDescription>
                    Fine-tune your agent's behavior
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="temperature">
                        Temperature: {agentData.temperature}
                      </Label>
                      <Input
                        id="temperature"
                        type="range"
                        min="0"
                        max="2"
                        step="0.1"
                        value={agentData.temperature}
                        onChange={(e) =>
                          handleInputChange(
                            "temperature",
                            parseFloat(e.target.value),
                          )
                        }
                      />
                      <div className="flex justify-between text-xs text-muted-foreground">
                        <span>Precise</span>
                        <span>Balanced</span>
                        <span>Creative</span>
                      </div>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="maxTokens">
                        Max Tokens: {agentData.maxTokens}
                      </Label>
                      <Input
                        id="maxTokens"
                        type="range"
                        min="100"
                        max="4000"
                        step="100"
                        value={agentData.maxTokens}
                        onChange={(e) =>
                          handleInputChange(
                            "maxTokens",
                            parseInt(e.target.value),
                          )
                        }
                      />
                      <div className="flex justify-between text-xs text-muted-foreground">
                        <span>Short</span>
                        <span>Medium</span>
                        <span>Long</span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="prompt" className="space-y-4 mt-4">
              <Card>
                <CardHeader>
                  <CardTitle>System Prompt</CardTitle>
                  <CardDescription>
                    Define the agent's personality and behavior
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Textarea
                    value={agentData.systemPrompt}
                    onChange={(e) =>
                      handleInputChange("systemPrompt", e.target.value)
                    }
                    rows={5}
                    className="font-mono"
                  />
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Prompt Template</CardTitle>
                  <CardDescription>
                    Design your prompt with parameter placeholders like{" "}
                    {`{{ parameter_name }}`}
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <Textarea
                    value={agentData.promptTemplate}
                    onChange={(e) =>
                      handleInputChange("promptTemplate", e.target.value)
                    }
                    rows={8}
                    className="font-mono"
                  />

                  <div className="flex justify-between items-center">
                    <div>
                      <h4 className="text-sm font-medium">
                        Available Parameters:
                      </h4>
                      <div className="flex flex-wrap gap-2 mt-1">
                        {agentData.parameters.map((param, index) => (
                          <Badge key={index} variant="outline">
                            {param.name}
                          </Badge>
                        ))}
                      </div>
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setActiveTab("params")}
                    >
                      <Settings className="mr-2 h-4 w-4" />
                      Manage Parameters
                    </Button>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Template Library</CardTitle>
                  <CardDescription>
                    Use pre-built templates to get started quickly
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ScrollArea className="h-[200px]">
                    <div className="space-y-2">
                      {savedTemplates.map((template) => (
                        <div
                          key={template.id}
                          className="flex items-center justify-between p-3 border rounded-md"
                        >
                          <div>
                            <h4 className="font-medium">{template.name}</h4>
                            <p className="text-sm text-muted-foreground">
                              {template.description}
                            </p>
                          </div>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => applyTemplate(template.id)}
                          >
                            <ChevronRight className="h-4 w-4" />
                            Apply
                          </Button>
                        </div>
                      ))}
                    </div>
                  </ScrollArea>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="params" className="space-y-4 mt-4">
              <Card>
                <CardHeader>
                  <CardTitle>Parameter Configuration</CardTitle>
                  <CardDescription>
                    Define the parameters your agent needs
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {agentData.parameters.map((param, index) => (
                      <div
                        key={index}
                        className="grid grid-cols-12 gap-4 items-start border p-4 rounded-md"
                      >
                        <div className="col-span-3 space-y-2">
                          <Label htmlFor={`param-name-${index}`}>Name</Label>
                          <Input
                            id={`param-name-${index}`}
                            value={param.name}
                            onChange={(e) =>
                              handleParameterChange(
                                index,
                                "name",
                                e.target.value,
                              )
                            }
                          />
                        </div>
                        <div className="col-span-2 space-y-2">
                          <Label htmlFor={`param-type-${index}`}>Type</Label>
                          <Select
                            value={param.type}
                            onValueChange={(value) =>
                              handleParameterChange(index, "type", value)
                            }
                          >
                            <SelectTrigger id={`param-type-${index}`}>
                              <SelectValue placeholder="Type" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="string">String</SelectItem>
                              <SelectItem value="number">Number</SelectItem>
                              <SelectItem value="boolean">Boolean</SelectItem>
                              <SelectItem value="array">Array</SelectItem>
                              <SelectItem value="object">Object</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                        <div className="col-span-5 space-y-2">
                          <Label htmlFor={`param-desc-${index}`}>
                            Description
                          </Label>
                          <Input
                            id={`param-desc-${index}`}
                            value={param.description}
                            onChange={(e) =>
                              handleParameterChange(
                                index,
                                "description",
                                e.target.value,
                              )
                            }
                          />
                        </div>
                        <div className="col-span-1 space-y-2">
                          <Label htmlFor={`param-req-${index}`}>Required</Label>
                          <div className="pt-2">
                            <Switch
                              id={`param-req-${index}`}
                              checked={param.required}
                              onCheckedChange={(checked) =>
                                handleParameterChange(
                                  index,
                                  "required",
                                  checked,
                                )
                              }
                            />
                          </div>
                        </div>
                        <div className="col-span-1 pt-8">
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => removeParameter(index)}
                            className="text-destructive hover:text-destructive hover:bg-destructive/10"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    ))}

                    <Button variant="outline" onClick={addParameter}>
                      <Plus className="mr-2 h-4 w-4" />
                      Add Parameter
                    </Button>
                  </div>
                </CardContent>
              </Card>

              <Alert>
                <AlertCircle className="h-4 w-4" />
                <AlertTitle>Parameter Tips</AlertTitle>
                <AlertDescription>
                  Parameters should be referenced in your prompt template using
                  double curly braces: {`{{ parameter_name }}`}
                </AlertDescription>
              </Alert>
            </TabsContent>
          </Tabs>
        </div>

        <div className="w-1/3 border-l p-4 overflow-auto">
          <Card className="h-full flex flex-col">
            <CardHeader>
              <CardTitle>Test Agent</CardTitle>
              <CardDescription>
                Try your agent with sample inputs
              </CardDescription>
            </CardHeader>
            <CardContent className="flex-1 overflow-auto space-y-4">
              <div className="space-y-4">
                {agentData.parameters.map((param, index) => (
                  <div key={index} className="space-y-2">
                    <Label htmlFor={`test-param-${param.name}`}>
                      {param.name}
                    </Label>
                    <Input
                      id={`test-param-${param.name}`}
                      value={testInput[param.name] || ""}
                      onChange={(e) =>
                        handleTestInputChange(param.name, e.target.value)
                      }
                      placeholder={param.description}
                    />
                  </div>
                ))}
              </div>

              <Separator />

              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <Label>Response</Label>
                  {testResponse && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() =>
                        navigator.clipboard.writeText(testResponse)
                      }
                    >
                      <Copy className="h-3 w-3 mr-1" />
                      Copy
                    </Button>
                  )}
                </div>
                <div className="min-h-[200px] max-h-[400px] overflow-auto border rounded-md p-4 bg-muted/30">
                  {isLoading ? (
                    <div className="flex items-center justify-center h-full">
                      <div className="animate-pulse text-muted-foreground">
                        Generating response...
                      </div>
                    </div>
                  ) : testResponse ? (
                    <div className="whitespace-pre-wrap">{testResponse}</div>
                  ) : (
                    <div className="text-muted-foreground text-center h-full flex items-center justify-center">
                      <div>Click "Test" to see the agent response</div>
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
            <CardFooter className="border-t pt-4">
              <div className="w-full flex justify-between items-center">
                <div className="flex items-center">
                  <Avatar className="h-8 w-8 mr-2">
                    <AvatarImage
                      src={`https://api.dicebear.com/7.x/avataaars/svg?seed=${agentData.name}`}
                    />
                    <AvatarFallback>
                      {agentData.name.substring(0, 2).toUpperCase()}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <p className="text-sm font-medium">{agentData.name}</p>
                    <p className="text-xs text-muted-foreground">
                      {agentData.model}
                    </p>
                  </div>
                </div>
                <Button onClick={handleTest} disabled={isLoading}>
                  {isLoading ? (
                    <div className="flex items-center">
                      <div className="animate-spin mr-2 h-4 w-4 border-2 border-current border-t-transparent rounded-full"></div>
                      Running...
                    </div>
                  ) : (
                    <>
                      <Play className="mr-2 h-4 w-4" />
                      Test Agent
                    </>
                  )}
                </Button>
              </div>
            </CardFooter>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default AgentBuilder;


