// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// User and Organization Management
model Organization {
  id          String   @id @default(cuid())
  name        String
  slug        String   @unique
  description String?
  settings    Json?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  users     User[]
  agents    Agent[]
  tools     Tool[]
  workflows Workflow[]
  sessions  Session[]

  @@map("organizations")
}

model User {
  id             String   @id @default(cuid())
  email          String   @unique
  passwordHash   String
  firstName      String
  lastName       String
  role           UserRole @default(USER)
  isActive       Boolean  @default(true)
  lastLoginAt    DateTime?
  emailVerified  <PERSON>olean  @default(false)
  organizationId String
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  // Relations
  organization Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  sessions     Session[]
  agents       Agent[]
  tools        Tool[]
  workflows    Workflow[]

  @@map("users")
}

enum UserRole {
  ADMIN
  DEVELOPER
  USER
  VIEWER
}

// AI Agents
model Agent {
  id             String      @id @default(cuid())
  name           String
  description    String?
  prompt         String
  model          String
  provider       String
  temperature    Float       @default(0.7)
  maxTokens      Int         @default(1000)
  systemPrompt   String?
  isActive       Boolean     @default(true)
  settings       Json?
  organizationId String
  userId         String
  createdAt      DateTime    @default(now())
  updatedAt      DateTime    @updatedAt

  // Relations
  organization Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  user         User         @relation(fields: [userId], references: [id])
  workflows    WorkflowStep[]
  sessions     Session[]

  @@map("agents")
}

// Tools
model Tool {
  id             String      @id @default(cuid())
  name           String
  description    String?
  type           ToolType
  endpoint       String?
  method         HttpMethod? @default(POST)
  headers        Json?
  authentication Json?
  inputSchema    Json
  outputSchema   Json?
  timeout        Int         @default(30000)
  retryCount     Int         @default(3)
  isActive       Boolean     @default(true)
  organizationId String
  userId         String
  createdAt      DateTime    @default(now())
  updatedAt      DateTime    @updatedAt

  // Relations
  organization Organization   @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  user         User           @relation(fields: [userId], references: [id])
  workflows    WorkflowStep[]

  @@map("tools")
}

enum ToolType {
  API
  WEBHOOK
  FUNCTION
  DATABASE
}

enum HttpMethod {
  GET
  POST
  PUT
  PATCH
  DELETE
}

// Workflows (Agent-Tool Hybrids)
model Workflow {
  id             String   @id @default(cuid())
  name           String
  description    String?
  isActive       Boolean  @default(true)
  settings       Json?
  organizationId String
  userId         String
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  // Relations
  organization Organization   @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  user         User           @relation(fields: [userId], references: [id])
  steps        WorkflowStep[]
  executions   WorkflowExecution[]

  @@map("workflows")
}

model WorkflowStep {
  id         String     @id @default(cuid())
  workflowId String
  order      Int
  type       StepType
  agentId    String?
  toolId     String?
  settings   Json?
  createdAt  DateTime   @default(now())
  updatedAt  DateTime   @updatedAt

  // Relations
  workflow Workflow @relation(fields: [workflowId], references: [id], onDelete: Cascade)
  agent    Agent?   @relation(fields: [agentId], references: [id])
  tool     Tool?    @relation(fields: [toolId], references: [id])

  @@map("workflow_steps")
}

enum StepType {
  AGENT
  TOOL
  CONDITION
  LOOP
}

model WorkflowExecution {
  id         String            @id @default(cuid())
  workflowId String
  status     ExecutionStatus   @default(RUNNING)
  input      Json?
  output     Json?
  error      String?
  startedAt  DateTime          @default(now())
  completedAt DateTime?

  // Relations
  workflow Workflow @relation(fields: [workflowId], references: [id], onDelete: Cascade)

  @@map("workflow_executions")
}

enum ExecutionStatus {
  PENDING
  RUNNING
  COMPLETED
  FAILED
  CANCELLED
}

// Sessions and State Management
model Session {
  id             String   @id @default(cuid())
  userId         String?
  agentId        String?
  organizationId String
  context        Json?
  memory         Json?
  expiresAt      DateTime
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  // Relations
  organization Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  user         User?        @relation(fields: [userId], references: [id])
  agent        Agent?       @relation(fields: [agentId], references: [id])

  @@map("sessions")
}

// Knowledge Base
model KnowledgeBase {
  id             String   @id @default(cuid())
  name           String
  description    String?
  organizationId String
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  // Relations
  documents Document[]

  @@map("knowledge_bases")
}

model Document {
  id              String        @id @default(cuid())
  knowledgeBaseId String
  title           String
  content         String
  metadata        Json?
  vectorEmbedding Float[]
  createdAt       DateTime      @default(now())
  updatedAt       DateTime      @updatedAt

  // Relations
  knowledgeBase KnowledgeBase @relation(fields: [knowledgeBaseId], references: [id], onDelete: Cascade)

  @@map("documents")
}
