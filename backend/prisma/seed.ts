import { PrismaClient, UserRole } from '@prisma/client';
import * as bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Starting database seed...');

  // Create default organization
  const organization = await prisma.organization.upsert({
    where: { slug: 'synapseai-demo' },
    update: {},
    create: {
      name: 'SynapseAI Demo',
      slug: 'synapseai-demo',
      description: 'Demo organization for SynapseAI',
      settings: {
        allowUserRegistration: true,
        defaultUserRole: 'USER',
        maxAgentsPerUser: 10,
        maxToolsPerUser: 20,
      },
    },
  });

  console.log('✅ Created organization:', organization.name);

  // Create admin user
  const adminPasswordHash = await bcrypt.hash('admin123', 12);
  const adminUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      passwordHash: adminPasswordHash,
      firstName: 'Admin',
      lastName: 'User',
      role: UserRole.ADMIN,
      isActive: true,
      emailVerified: true,
      organizationId: organization.id,
    },
  });

  console.log('✅ Created admin user:', adminUser.email);

  // Create demo user
  const demoPasswordHash = await bcrypt.hash('demo123', 12);
  const demoUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      passwordHash: demoPasswordHash,
      firstName: 'Demo',
      lastName: 'User',
      role: UserRole.USER,
      isActive: true,
      emailVerified: true,
      organizationId: organization.id,
    },
  });

  console.log('✅ Created demo user:', demoUser.email);

  // Create sample agent
  const sampleAgent = await prisma.agent.create({
    data: {
      name: 'Customer Support Assistant',
      description: 'A helpful AI assistant for customer support inquiries',
      prompt: 'You are a helpful customer support assistant. Be friendly, professional, and provide accurate information.',
      model: 'gpt-4',
      provider: 'openai',
      temperature: 0.7,
      maxTokens: 1000,
      systemPrompt: 'Always be helpful and professional in your responses.',
      isActive: true,
      settings: {
        enableMemory: true,
        maxMemoryLength: 10,
        responseFormat: 'text',
      },
      organizationId: organization.id,
      userId: adminUser.id,
    },
  });

  console.log('✅ Created sample agent:', sampleAgent.name);

  // Create sample tool
  const sampleTool = await prisma.tool.create({
    data: {
      name: 'Weather API',
      description: 'Get current weather information for any city',
      type: 'API',
      endpoint: 'https://api.openweathermap.org/data/2.5/weather',
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
      authentication: {
        type: 'query_param',
        key: 'appid',
        value: '${WEATHER_API_KEY}',
      },
      inputSchema: {
        type: 'object',
        properties: {
          city: {
            type: 'string',
            description: 'City name to get weather for',
          },
          units: {
            type: 'string',
            enum: ['metric', 'imperial', 'kelvin'],
            default: 'metric',
          },
        },
        required: ['city'],
      },
      outputSchema: {
        type: 'object',
        properties: {
          temperature: { type: 'number' },
          description: { type: 'string' },
          humidity: { type: 'number' },
        },
      },
      timeout: 5000,
      retryCount: 2,
      isActive: true,
      organizationId: organization.id,
      userId: adminUser.id,
    },
  });

  console.log('✅ Created sample tool:', sampleTool.name);

  // Create sample workflow
  const sampleWorkflow = await prisma.workflow.create({
    data: {
      name: 'Weather Assistant Workflow',
      description: 'A workflow that combines the customer support agent with weather tool',
      isActive: true,
      settings: {
        autoStart: false,
        maxExecutionTime: 300000, // 5 minutes
      },
      organizationId: organization.id,
      userId: adminUser.id,
      steps: {
        create: [
          {
            order: 1,
            type: 'AGENT',
            agentId: sampleAgent.id,
            settings: {
              prompt: 'Analyze the user request and determine if weather information is needed.',
            },
          },
          {
            order: 2,
            type: 'TOOL',
            toolId: sampleTool.id,
            settings: {
              condition: 'weather_needed',
              parameters: {
                city: '${user_city}',
                units: 'metric',
              },
            },
          },
          {
            order: 3,
            type: 'AGENT',
            agentId: sampleAgent.id,
            settings: {
              prompt: 'Provide a helpful response using the weather data if available.',
            },
          },
        ],
      },
    },
  });

  console.log('✅ Created sample workflow:', sampleWorkflow.name);

  // Create knowledge base
  const knowledgeBase = await prisma.knowledgeBase.create({
    data: {
      name: 'Company Knowledge Base',
      description: 'Internal company documentation and FAQs',
      organizationId: organization.id,
      documents: {
        create: [
          {
            title: 'Getting Started Guide',
            content: 'Welcome to SynapseAI! This guide will help you get started with creating your first AI agent...',
            metadata: {
              category: 'documentation',
              tags: ['getting-started', 'tutorial'],
              author: 'SynapseAI Team',
            },
            vectorEmbedding: [], // Would be populated by embedding service
          },
          {
            title: 'API Documentation',
            content: 'SynapseAI provides a comprehensive REST API for managing agents, tools, and workflows...',
            metadata: {
              category: 'api',
              tags: ['api', 'reference'],
              author: 'SynapseAI Team',
            },
            vectorEmbedding: [], // Would be populated by embedding service
          },
        ],
      },
    },
  });

  console.log('✅ Created knowledge base:', knowledgeBase.name);

  console.log('🎉 Database seed completed successfully!');
  console.log('\n📋 Summary:');
  console.log(`- Organization: ${organization.name} (${organization.slug})`);
  console.log(`- Admin User: ${adminUser.email} (password: admin123)`);
  console.log(`- Demo User: ${demoUser.email} (password: demo123)`);
  console.log(`- Sample Agent: ${sampleAgent.name}`);
  console.log(`- Sample Tool: ${sampleTool.name}`);
  console.log(`- Sample Workflow: ${sampleWorkflow.name}`);
  console.log(`- Knowledge Base: ${knowledgeBase.name}`);
}

main()
  .catch((e) => {
    console.error('❌ Seed failed:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
