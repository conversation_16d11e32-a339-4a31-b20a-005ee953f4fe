# SynapseAI Backend

A universal, event-based, click-configurable AI orchestration system built with NestJS, PostgreSQL, and Redis.

## 🏗️ Architecture Overview

SynapseAI Backend is a production-ready, enterprise-grade AI orchestration platform that provides:

- **Universal AI Provider Integration** - OpenAI, Claude, Gemini, Mistral, Groq with smart routing
- **Event-Based Architecture** - Real-time WebSocket communication with APIX protocol
- **Click-Configurable Agents** - No-code agent creation and management
- **Tool Execution Engine** - Stateless, secure tool execution with retry mechanisms
- **Hybrid Workflows** - Combine memory-aware agents with stateless tools
- **Multi-Tenant Security** - RBAC, organization isolation, input validation
- **Session Management** - Redis-backed state persistence and memory

## 🚀 Quick Start

### Prerequisites

- Node.js 18.17.0 or higher
- PostgreSQL 12+
- Redis 6+
- Yarn package manager

### Installation

1. **Install dependencies:**
```bash
yarn install
```

2. **Set up environment variables:**
```bash
cp .env.example .env
# Edit .env with your database and Redis credentials
```

3. **Generate Prisma client:**
```bash
yarn db:generate
```

4. **Set up database:**
```bash
# For development (creates tables)
yarn db:push

# For production (with migrations)
yarn db:migrate

# Seed with sample data
yarn db:seed
```

5. **Start the backend:**
```bash
# Development mode
yarn backend:dev

# Production mode
yarn backend:build
yarn backend:start
```

The backend will be available at `http://localhost:3001` with API documentation at `http://localhost:3001/api/docs`.

## 📁 Project Structure

```
backend/
├── src/
│   ├── auth/              # JWT authentication & RBAC
│   ├── users/             # User management
│   ├── agents/            # AI agent configuration
│   ├── tools/             # Tool execution engine
│   ├── workflows/         # Agent-tool hybrid workflows
│   ├── providers/         # AI provider integrations
│   ├── sessions/          # Session & memory management
│   ├── websocket/         # Real-time WebSocket gateway
│   ├── database/          # Prisma database layer
│   ├── redis/             # Redis caching & sessions
│   ├── uaui/              # Universal AI UI engine
│   ├── common/            # Shared guards, pipes, interceptors
│   ├── main.ts            # Application entry point
│   └── app.module.ts      # Root module
├── prisma/
│   ├── schema.prisma      # Database schema
│   └── seed.ts            # Sample data
├── test-backend.js        # Comprehensive test suite
└── README.md
```

## 🔧 Core Modules

### 1. Authentication & Authorization
- **JWT-based authentication** with refresh tokens
- **Role-Based Access Control (RBAC)** - Admin, Developer, User, Viewer
- **Multi-tenant organization isolation**
- **Session management** with Redis persistence

### 2. AI Provider Integration
- **Smart Provider Selector** with automatic failover
- **Real AI integrations**: OpenAI, Anthropic, Google AI, Mistral, Groq
- **Cost tracking and usage analytics**
- **Retry logic with exponential backoff**

### 3. Agent Management
- **Memory-aware agents** with conversation history
- **Configurable prompts and parameters**
- **Model and provider selection**
- **Real-time streaming responses**

### 4. Tool Execution Engine
- **Stateless tool execution** with input/output validation
- **Multiple tool types**: API, Webhook, Function, Database
- **Secure execution environment** with timeout and retry
- **Zod schema validation** for parameters

### 5. Workflow Engine
- **Hybrid agent-tool workflows** with visual flow builder
- **Context merging** between steps
- **Conditional logic and loops**
- **Real-time execution monitoring**

### 6. UAUI (Universal AI UI) Engine
- **Cross-application state management**
- **Event-driven architecture** with pub/sub
- **Router engine** for UI commands
- **Session-aware context injection**

### 7. WebSocket Gateway (APIX Protocol)
- **Real-time bidirectional communication**
- **Event broadcasting** to users and organizations
- **Connection management** with authentication
- **Message queuing and delivery guarantees**

## 🔒 Security Features

### Input Validation & Sanitization
- **Zod schema validation** throughout the application
- **XSS protection** with DOMPurify
- **SQL injection prevention**
- **Request size limits** and timeout protection

### Security Headers & Middleware
- **Helmet.js** for security headers
- **Rate limiting** per endpoint and user
- **CORS configuration** with origin validation
- **Content Security Policy (CSP)**

### Authentication & Authorization
- **JWT tokens** with secure secret rotation
- **Organization-level resource isolation**
- **Role-based permissions** on all endpoints
- **Session invalidation** and logout

## 📊 API Endpoints

### Authentication
- `POST /api/auth/register` - User registration
- `POST /api/auth/login` - User login
- `POST /api/auth/refresh` - Token refresh
- `POST /api/auth/logout` - User logout
- `GET /api/auth/me` - Current user profile

### Agents
- `GET /api/agents` - List agents
- `POST /api/agents` - Create agent
- `GET /api/agents/:id` - Get agent details
- `PATCH /api/agents/:id` - Update agent
- `DELETE /api/agents/:id` - Delete agent

### Tools
- `GET /api/tools` - List tools
- `POST /api/tools` - Create tool
- `POST /api/tools/:id/execute` - Execute tool
- `GET /api/tools/:id/stats` - Tool statistics

### Workflows
- `GET /api/workflows` - List workflows
- `POST /api/workflows` - Create workflow
- `POST /api/workflows/:id/execute` - Execute workflow
- `GET /api/workflows/executions/:id` - Execution status

### UAUI
- `POST /api/uaui/process` - Process AI request
- `POST /api/uaui/state/:key` - Set application state
- `GET /api/uaui/state/:key` - Get application state
- `POST /api/uaui/events/emit` - Emit custom event

## 🌐 WebSocket Events (APIX Protocol)

### Client → Server
- `user_message` - Send message to agent
- `tool_call_start` - Tool execution started
- `state_update` - Update application state
- `user_response` - Response to HITL request

### Server → Client
- `text_chunk` - Streaming text response
- `thinking_status` - Agent processing status
- `tool_call_result` - Tool execution result
- `request_user_input` - Human-in-the-loop request
- `workflow_completed` - Workflow finished
- `error` - Error notification

## 🧪 Testing

Run the comprehensive test suite:

```bash
cd backend
node test-backend.js
```

The test suite validates:
- ✅ File structure completeness
- ✅ TypeScript compilation
- ✅ Prisma schema validation
- ✅ Dependency verification
- ✅ Module structure integrity
- ✅ Security configuration

## 🔧 Configuration

### Environment Variables

```bash
# Database
DATABASE_URL="postgresql://user:pass@localhost:5432/synapseai"

# Redis
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# JWT
JWT_SECRET=your-super-secret-jwt-key
JWT_EXPIRES_IN=24h

# Application
PORT=3001
FRONTEND_URL=http://localhost:3000
NODE_ENV=development

# AI Providers
OPENAI_API_KEY=your-openai-api-key
ANTHROPIC_API_KEY=your-anthropic-api-key
GOOGLE_AI_API_KEY=your-google-ai-api-key
MISTRAL_API_KEY=your-mistral-api-key
GROQ_API_KEY=your-groq-api-key
```

## 🚀 Deployment

### Production Checklist

1. **Environment Setup**
   - Set `NODE_ENV=production`
   - Configure secure JWT secrets
   - Set up SSL certificates
   - Configure production database

2. **Security Hardening**
   - Enable rate limiting
   - Configure CORS for production domains
   - Set up monitoring and alerting
   - Enable audit logging

3. **Performance Optimization**
   - Configure Redis clustering
   - Set up database connection pooling
   - Enable response compression
   - Configure CDN for static assets

### Docker Deployment

```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN yarn install --production
COPY . .
RUN yarn backend:build
EXPOSE 3001
CMD ["yarn", "backend:start"]
```

## 📈 Monitoring & Analytics

The backend provides comprehensive monitoring:

- **Health checks** for all services
- **Performance metrics** for AI providers
- **Usage analytics** per organization
- **Error tracking** with detailed logs
- **Real-time connection monitoring**

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Run tests: `node test-backend.js`
4. Submit a pull request

## 📄 License

This project is licensed under the MIT License.
