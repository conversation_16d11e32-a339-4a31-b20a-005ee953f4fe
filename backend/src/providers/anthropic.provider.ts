import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AIProvider, AIRequest, AIResponse, AIStreamChunk, AIModel, ProviderConfig } from './interfaces/provider.interface';

@Injectable()
export class AnthropicProvider implements AIProvider {
  public readonly id = 'anthropic';
  public readonly name = 'Anthropic';
  private readonly logger = new Logger(AnthropicProvider.name);
  private readonly config: ProviderConfig;

  constructor(private readonly configService: ConfigService) {
    this.config = {
      apiKey: this.configService.get<string>('ANTHROPIC_API_KEY'),
      baseUrl: 'https://api.anthropic.com/v1',
      timeout: 30000,
      retryAttempts: 3,
      retryDelay: 1000,
    };
  }

  async generateResponse(request: AIRequest): Promise<AIResponse> {
    try {
      const messages = this.formatMessages(request.messages);
      const response = await this.makeRequest('/messages', {
        model: request.model,
        messages,
        max_tokens: request.maxTokens || 1000,
        temperature: request.temperature || 0.7,
        stream: false,
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(`Anthropic API error: ${data.error?.message || 'Unknown error'}`);
      }

      const usage = data.usage;
      const cost = this.calculateCost(usage.input_tokens + usage.output_tokens, request.model);

      return {
        content: data.content[0].text,
        usage: {
          promptTokens: usage.input_tokens,
          completionTokens: usage.output_tokens,
          totalTokens: usage.input_tokens + usage.output_tokens,
        },
        model: data.model,
        finishReason: data.stop_reason,
        cost,
      };
    } catch (error) {
      this.logger.error(`Anthropic API error: ${error.message}`);
      throw error;
    }
  }

  async *streamResponse(request: AIRequest): AsyncGenerator<AIStreamChunk> {
    try {
      const messages = this.formatMessages(request.messages);
      const response = await this.makeRequest('/messages', {
        model: request.model,
        messages,
        max_tokens: request.maxTokens || 1000,
        temperature: request.temperature || 0.7,
        stream: true,
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(`Anthropic API error: ${error.error?.message || 'Unknown error'}`);
      }

      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error('No response body');
      }

      const decoder = new TextDecoder();
      let buffer = '';

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');
        buffer = lines.pop() || '';

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6);
            
            try {
              const parsed = JSON.parse(data);
              
              if (parsed.type === 'content_block_delta') {
                yield {
                  content: parsed.delta.text || '',
                  isComplete: false,
                };
              } else if (parsed.type === 'message_stop') {
                yield { content: '', isComplete: true };
                return;
              }
            } catch (e) {
              // Skip invalid JSON
            }
          }
        }
      }
    } catch (error) {
      this.logger.error(`Anthropic streaming error: ${error.message}`);
      throw error;
    }
  }

  async getModels(): Promise<AIModel[]> {
    return [
      {
        id: 'claude-3-opus-20240229',
        name: 'Claude 3 Opus',
        description: 'Most powerful Claude model',
        contextLength: 200000,
        inputCostPer1kTokens: 0.015,
        outputCostPer1kTokens: 0.075,
        isAvailable: true,
      },
      {
        id: 'claude-3-sonnet-20240229',
        name: 'Claude 3 Sonnet',
        description: 'Balanced performance and speed',
        contextLength: 200000,
        inputCostPer1kTokens: 0.003,
        outputCostPer1kTokens: 0.015,
        isAvailable: true,
      },
      {
        id: 'claude-3-haiku-20240307',
        name: 'Claude 3 Haiku',
        description: 'Fastest Claude model',
        contextLength: 200000,
        inputCostPer1kTokens: 0.00025,
        outputCostPer1kTokens: 0.00125,
        isAvailable: true,
      },
    ];
  }

  calculateCost(tokens: number, model: string): number {
    const models = {
      'claude-3-opus-20240229': { input: 0.015, output: 0.075 },
      'claude-3-sonnet-20240229': { input: 0.003, output: 0.015 },
      'claude-3-haiku-20240307': { input: 0.00025, output: 0.00125 },
    };

    const pricing = models[model] || models['claude-3-haiku-20240307'];
    // Simplified cost calculation (assumes 50/50 input/output split)
    return (tokens / 1000) * ((pricing.input + pricing.output) / 2);
  }

  async validateApiKey(apiKey: string): Promise<boolean> {
    try {
      const response = await fetch(`${this.config.baseUrl}/messages`, {
        method: 'POST',
        headers: {
          'x-api-key': apiKey,
          'Content-Type': 'application/json',
          'anthropic-version': '2023-06-01',
        },
        body: JSON.stringify({
          model: 'claude-3-haiku-20240307',
          messages: [{ role: 'user', content: 'test' }],
          max_tokens: 1,
        }),
      });
      return response.status !== 401;
    } catch {
      return false;
    }
  }

  private formatMessages(messages: any[]): any[] {
    // Anthropic doesn't support system messages in the same way
    // Convert system messages to user messages with context
    return messages
      .filter(msg => msg.role !== 'system')
      .map(msg => ({
        role: msg.role === 'assistant' ? 'assistant' : 'user',
        content: msg.content,
      }));
  }

  private async makeRequest(endpoint: string, body: any): Promise<Response> {
    const url = `${this.config.baseUrl}${endpoint}`;
    
    for (let attempt = 0; attempt < this.config.retryAttempts; attempt++) {
      try {
        const response = await fetch(url, {
          method: 'POST',
          headers: {
            'x-api-key': this.config.apiKey,
            'Content-Type': 'application/json',
            'anthropic-version': '2023-06-01',
          },
          body: JSON.stringify(body),
          signal: AbortSignal.timeout(this.config.timeout),
        });

        if (response.ok || response.status < 500) {
          return response;
        }

        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      } catch (error) {
        if (attempt === this.config.retryAttempts - 1) {
          throw error;
        }
        
        await new Promise(resolve => 
          setTimeout(resolve, this.config.retryDelay * Math.pow(2, attempt))
        );
      }
    }

    throw new Error('Max retry attempts reached');
  }
}
