export interface AIProvider {
  id: string;
  name: string;
  generateResponse(request: AIRequest): Promise<AIResponse>;
  streamResponse(request: AIRequest): AsyncGenerator<AIStreamChunk>;
  getModels(): Promise<AIModel[]>;
  calculateCost(tokens: number, model: string): number;
  validateApiKey(apiKey: string): Promise<boolean>;
}

export interface AIRequest {
  model: string;
  messages: AIMessage[];
  temperature?: number;
  maxTokens?: number;
  stream?: boolean;
  systemPrompt?: string;
  userId?: string;
  sessionId?: string;
}

export interface AIMessage {
  role: 'system' | 'user' | 'assistant';
  content: string;
}

export interface AIResponse {
  content: string;
  usage: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
  model: string;
  finishReason: string;
  cost?: number;
}

export interface AIStreamChunk {
  content: string;
  isComplete: boolean;
  usage?: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
}

export interface AIModel {
  id: string;
  name: string;
  description?: string;
  contextLength: number;
  inputCostPer1kTokens: number;
  outputCostPer1kTokens: number;
  isAvailable: boolean;
}

export interface ProviderConfig {
  apiKey: string;
  baseUrl?: string;
  timeout?: number;
  retryAttempts?: number;
  retryDelay?: number;
}
