import { Module } from '@nestjs/common';
import { ProvidersService } from './providers.service';
import { ProvidersController } from './providers.controller';
import { SmartProviderSelector } from './smart-provider-selector.service';
import { OpenAIProvider } from './openai.provider';
import { AnthropicProvider } from './anthropic.provider';

@Module({
  providers: [
    ProvidersService,
    SmartProviderSelector,
    OpenAIProvider,
    AnthropicProvider,
  ],
  controllers: [ProvidersController],
  exports: [ProvidersService, SmartProviderSelector],
})
export class ProvidersModule {}
