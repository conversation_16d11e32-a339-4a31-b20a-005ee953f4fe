import { Controller, Get, Post, Param, UseGuards, Request } from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { ProvidersService } from './providers.service';

@ApiTags('Providers')
@Controller('api/providers')
@UseGuards(AuthGuard('jwt'))
@ApiBearerAuth()
export class ProvidersController {
  constructor(private readonly providersService: ProvidersService) {}

  @Get()
  @ApiOperation({ summary: 'Get available AI providers' })
  @ApiResponse({ status: 200, description: 'Providers retrieved successfully' })
  getProviders() {
    return this.providersService.getAvailableProviders();
  }

  @Post(':id/test')
  @ApiOperation({ summary: 'Test AI provider connection' })
  @ApiResponse({ status: 200, description: 'Provider test completed' })
  testProvider(@Param('id') id: string) {
    return this.providersService.testProvider(id);
  }

  @Get('usage')
  @ApiOperation({ summary: 'Get provider usage statistics' })
  @ApiResponse({ status: 200, description: 'Usage statistics retrieved' })
  getUsage(@Request() req) {
    return this.providersService.getProviderUsage(req.user.organizationId);
  }
}
