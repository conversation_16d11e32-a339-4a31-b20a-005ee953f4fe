import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { SmartProviderSelector } from './smart-provider-selector.service';
import { AIRequest, AIResponse } from './interfaces/provider.interface';

@Injectable()
export class ProvidersService {
  private readonly logger = new Logger(ProvidersService.name);

  constructor(
    private readonly configService: ConfigService,
    private readonly smartSelector: SmartProviderSelector,
  ) {}

  async getAvailableProviders() {
    const providers = this.smartSelector.getAllProviders();
    const result = [];

    for (const provider of providers) {
      const models = await provider.getModels();
      result.push({
        id: provider.id,
        name: provider.name,
        models: models.map(m => m.id),
        isConfigured: await this.isProviderConfigured(provider.id),
        modelDetails: models,
      });
    }

    return result;
  }

  async generateResponse(request: AIRequest): Promise<AIResponse> {
    const startTime = Date.now();
    let selectedProvider;
    let success = false;
    let cost = 0;

    try {
      selectedProvider = await this.smartSelector.selectProvider(request);
      const response = await selectedProvider.generateResponse(request);

      success = true;
      cost = response.cost || 0;

      return response;
    } catch (error) {
      this.logger.error(`Provider request failed: ${error.message}`);
      throw error;
    } finally {
      if (selectedProvider) {
        const latency = Date.now() - startTime;
        await this.smartSelector.recordProviderMetrics(
          selectedProvider.id,
          latency,
          success,
          cost,
        );
      }
    }
  }

  async *streamResponse(request: AIRequest): AsyncGenerator<any> {
    const startTime = Date.now();
    let selectedProvider;
    let success = false;
    let cost = 0;

    try {
      selectedProvider = await this.smartSelector.selectProvider(request);

      for await (const chunk of selectedProvider.streamResponse(request)) {
        yield chunk;
        if (chunk.isComplete) {
          success = true;
          cost = chunk.usage ? selectedProvider.calculateCost(chunk.usage.totalTokens, request.model) : 0;
        }
      }
    } catch (error) {
      this.logger.error(`Provider streaming failed: ${error.message}`);
      throw error;
    } finally {
      if (selectedProvider) {
        const latency = Date.now() - startTime;
        await this.smartSelector.recordProviderMetrics(
          selectedProvider.id,
          latency,
          success,
          cost,
        );
      }
    }
  }

  async testProvider(providerId: string) {
    try {
      const provider = this.smartSelector.getProvider(providerId);
      if (!provider) {
        return { success: false, message: 'Provider not found' };
      }

      const testRequest: AIRequest = {
        model: (await provider.getModels())[0]?.id || 'default',
        messages: [{ role: 'user', content: 'Hello, this is a test message.' }],
        maxTokens: 10,
      };

      const response = await provider.generateResponse(testRequest);
      return {
        success: true,
        message: 'Provider test successful',
        response: response.content.substring(0, 100),
      };
    } catch (error) {
      return {
        success: false,
        message: `Provider test failed: ${error.message}`,
      };
    }
  }

  async getProviderUsage(organizationId: string) {
    return this.smartSelector.getProviderStats(organizationId);
  }

  private async isProviderConfigured(providerId: string): Promise<boolean> {
    const keyMap = {
      'openai': 'OPENAI_API_KEY',
      'anthropic': 'ANTHROPIC_API_KEY',
      'google': 'GOOGLE_AI_API_KEY',
      'mistral': 'MISTRAL_API_KEY',
      'groq': 'GROQ_API_KEY',
    };

    const apiKey = this.configService.get(keyMap[providerId]);
    return !!apiKey;
  }
}
