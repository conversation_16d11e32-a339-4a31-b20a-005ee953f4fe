import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AIProvider, AIRequest, AIResponse, AIStreamChunk, AIModel, ProviderConfig } from './interfaces/provider.interface';

@Injectable()
export class OpenAIProvider implements AIProvider {
  public readonly id = 'openai';
  public readonly name = 'OpenAI';
  private readonly logger = new Logger(OpenAIProvider.name);
  private readonly config: ProviderConfig;

  constructor(private readonly configService: ConfigService) {
    this.config = {
      apiKey: this.configService.get<string>('OPENAI_API_KEY'),
      baseUrl: 'https://api.openai.com/v1',
      timeout: 30000,
      retryAttempts: 3,
      retryDelay: 1000,
    };
  }

  async generateResponse(request: AIRequest): Promise<AIResponse> {
    try {
      const response = await this.makeRequest('/chat/completions', {
        model: request.model,
        messages: request.messages,
        temperature: request.temperature || 0.7,
        max_tokens: request.maxTokens || 1000,
        stream: false,
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(`OpenAI API error: ${data.error?.message || 'Unknown error'}`);
      }

      const usage = data.usage;
      const cost = this.calculateCost(usage.total_tokens, request.model);

      return {
        content: data.choices[0].message.content,
        usage: {
          promptTokens: usage.prompt_tokens,
          completionTokens: usage.completion_tokens,
          totalTokens: usage.total_tokens,
        },
        model: data.model,
        finishReason: data.choices[0].finish_reason,
        cost,
      };
    } catch (error) {
      this.logger.error(`OpenAI API error: ${error.message}`);
      throw error;
    }
  }

  async *streamResponse(request: AIRequest): AsyncGenerator<AIStreamChunk> {
    try {
      const response = await this.makeRequest('/chat/completions', {
        model: request.model,
        messages: request.messages,
        temperature: request.temperature || 0.7,
        max_tokens: request.maxTokens || 1000,
        stream: true,
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(`OpenAI API error: ${error.error?.message || 'Unknown error'}`);
      }

      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error('No response body');
      }

      const decoder = new TextDecoder();
      let buffer = '';

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');
        buffer = lines.pop() || '';

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6);
            if (data === '[DONE]') {
              yield { content: '', isComplete: true };
              return;
            }

            try {
              const parsed = JSON.parse(data);
              const delta = parsed.choices[0]?.delta;
              if (delta?.content) {
                yield {
                  content: delta.content,
                  isComplete: false,
                };
              }
            } catch (e) {
              // Skip invalid JSON
            }
          }
        }
      }
    } catch (error) {
      this.logger.error(`OpenAI streaming error: ${error.message}`);
      throw error;
    }
  }

  async getModels(): Promise<AIModel[]> {
    return [
      {
        id: 'gpt-4',
        name: 'GPT-4',
        description: 'Most capable GPT-4 model',
        contextLength: 8192,
        inputCostPer1kTokens: 0.03,
        outputCostPer1kTokens: 0.06,
        isAvailable: true,
      },
      {
        id: 'gpt-4-turbo',
        name: 'GPT-4 Turbo',
        description: 'Latest GPT-4 model with improved performance',
        contextLength: 128000,
        inputCostPer1kTokens: 0.01,
        outputCostPer1kTokens: 0.03,
        isAvailable: true,
      },
      {
        id: 'gpt-3.5-turbo',
        name: 'GPT-3.5 Turbo',
        description: 'Fast and efficient model',
        contextLength: 16385,
        inputCostPer1kTokens: 0.0015,
        outputCostPer1kTokens: 0.002,
        isAvailable: true,
      },
    ];
  }

  calculateCost(tokens: number, model: string): number {
    const models = {
      'gpt-4': { input: 0.03, output: 0.06 },
      'gpt-4-turbo': { input: 0.01, output: 0.03 },
      'gpt-3.5-turbo': { input: 0.0015, output: 0.002 },
    };

    const pricing = models[model] || models['gpt-3.5-turbo'];
    // Simplified cost calculation (assumes 50/50 input/output split)
    return (tokens / 1000) * ((pricing.input + pricing.output) / 2);
  }

  async validateApiKey(apiKey: string): Promise<boolean> {
    try {
      const response = await fetch(`${this.config.baseUrl}/models`, {
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json',
        },
      });
      return response.ok;
    } catch {
      return false;
    }
  }

  private async makeRequest(endpoint: string, body: any): Promise<Response> {
    const url = `${this.config.baseUrl}${endpoint}`;
    
    for (let attempt = 0; attempt < this.config.retryAttempts; attempt++) {
      try {
        const response = await fetch(url, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${this.config.apiKey}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(body),
          signal: AbortSignal.timeout(this.config.timeout),
        });

        if (response.ok || response.status < 500) {
          return response;
        }

        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      } catch (error) {
        if (attempt === this.config.retryAttempts - 1) {
          throw error;
        }
        
        await new Promise(resolve => 
          setTimeout(resolve, this.config.retryDelay * Math.pow(2, attempt))
        );
      }
    }

    throw new Error('Max retry attempts reached');
  }
}
