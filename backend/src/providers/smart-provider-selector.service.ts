import { Injectable, Logger } from '@nestjs/common';
import { RedisService } from '../redis/redis.service';
import { AIProvider, AIRequest } from './interfaces/provider.interface';
import { OpenAIProvider } from './openai.provider';
import { AnthropicProvider } from './anthropic.provider';

interface ProviderMetrics {
  providerId: string;
  averageLatency: number;
  successRate: number;
  totalRequests: number;
  lastUpdated: Date;
  cost: number;
}

interface SelectionCriteria {
  prioritizeSpeed?: boolean;
  prioritizeCost?: boolean;
  prioritizeReliability?: boolean;
  maxLatency?: number;
  maxCost?: number;
  excludeProviders?: string[];
}

@Injectable()
export class SmartProviderSelector {
  private readonly logger = new Logger(SmartProviderSelector.name);
  private readonly providers = new Map<string, AIProvider>();

  constructor(
    private readonly redisService: RedisService,
    private readonly openaiProvider: OpenAIProvider,
    private readonly anthropicProvider: AnthropicProvider,
  ) {
    this.providers.set('openai', this.openaiProvider);
    this.providers.set('anthropic', this.anthropicProvider);
  }

  async selectProvider(
    request: AIRequest,
    criteria: SelectionCriteria = {},
  ): Promise<AIProvider> {
    try {
      // Get available providers
      const availableProviders = await this.getAvailableProviders(request.model);
      
      if (availableProviders.length === 0) {
        throw new Error('No available providers for the requested model');
      }

      // If only one provider available, return it
      if (availableProviders.length === 1) {
        return availableProviders[0];
      }

      // Get metrics for each provider
      const providerMetrics = await Promise.all(
        availableProviders.map(provider => this.getProviderMetrics(provider.id))
      );

      // Score providers based on criteria
      const scoredProviders = providerMetrics.map((metrics, index) => ({
        provider: availableProviders[index],
        score: this.calculateProviderScore(metrics, criteria),
        metrics,
      }));

      // Sort by score (higher is better)
      scoredProviders.sort((a, b) => b.score - a.score);

      const selectedProvider = scoredProviders[0].provider;
      
      this.logger.log(
        `Selected provider: ${selectedProvider.id} (score: ${scoredProviders[0].score.toFixed(2)})`
      );

      return selectedProvider;
    } catch (error) {
      this.logger.error(`Provider selection failed: ${error.message}`);
      // Fallback to first available provider
      return this.providers.values().next().value;
    }
  }

  async recordProviderMetrics(
    providerId: string,
    latency: number,
    success: boolean,
    cost: number,
  ): Promise<void> {
    try {
      const key = `provider_metrics:${providerId}`;
      const existing = await this.redisService.get(key);
      
      let metrics: ProviderMetrics;
      
      if (existing) {
        metrics = existing;
        // Update running averages
        const totalRequests = metrics.totalRequests + 1;
        metrics.averageLatency = 
          (metrics.averageLatency * metrics.totalRequests + latency) / totalRequests;
        metrics.successRate = 
          (metrics.successRate * metrics.totalRequests + (success ? 1 : 0)) / totalRequests;
        metrics.cost = 
          (metrics.cost * metrics.totalRequests + cost) / totalRequests;
        metrics.totalRequests = totalRequests;
        metrics.lastUpdated = new Date();
      } else {
        metrics = {
          providerId,
          averageLatency: latency,
          successRate: success ? 1 : 0,
          totalRequests: 1,
          lastUpdated: new Date(),
          cost,
        };
      }

      // Store metrics with 7-day TTL
      await this.redisService.set(key, metrics, 7 * 24 * 60 * 60);
    } catch (error) {
      this.logger.error(`Failed to record metrics for ${providerId}: ${error.message}`);
    }
  }

  async getProviderMetrics(providerId: string): Promise<ProviderMetrics> {
    try {
      const metrics = await this.redisService.get(`provider_metrics:${providerId}`);
      
      if (metrics) {
        return metrics;
      }

      // Return default metrics for new providers
      return {
        providerId,
        averageLatency: 5000, // Default 5 seconds
        successRate: 0.95, // Default 95% success rate
        totalRequests: 0,
        lastUpdated: new Date(),
        cost: 0.01, // Default cost
      };
    } catch (error) {
      this.logger.error(`Failed to get metrics for ${providerId}: ${error.message}`);
      return {
        providerId,
        averageLatency: 10000,
        successRate: 0.5,
        totalRequests: 0,
        lastUpdated: new Date(),
        cost: 0.05,
      };
    }
  }

  private async getAvailableProviders(model: string): Promise<AIProvider[]> {
    const available: AIProvider[] = [];

    for (const provider of this.providers.values()) {
      try {
        const models = await provider.getModels();
        const hasModel = models.some(m => m.id === model && m.isAvailable);
        
        if (hasModel) {
          available.push(provider);
        }
      } catch (error) {
        this.logger.warn(`Provider ${provider.id} unavailable: ${error.message}`);
      }
    }

    return available;
  }

  private calculateProviderScore(
    metrics: ProviderMetrics,
    criteria: SelectionCriteria,
  ): number {
    let score = 0;
    let totalWeight = 0;

    // Reliability score (0-100)
    const reliabilityWeight = criteria.prioritizeReliability ? 50 : 30;
    const reliabilityScore = metrics.successRate * 100;
    score += reliabilityScore * reliabilityWeight;
    totalWeight += reliabilityWeight;

    // Speed score (0-100, inverted latency)
    const speedWeight = criteria.prioritizeSpeed ? 50 : 25;
    const maxLatency = criteria.maxLatency || 30000; // 30 seconds max
    const speedScore = Math.max(0, 100 - (metrics.averageLatency / maxLatency) * 100);
    score += speedScore * speedWeight;
    totalWeight += speedWeight;

    // Cost score (0-100, inverted cost)
    const costWeight = criteria.prioritizeCost ? 50 : 25;
    const maxCost = criteria.maxCost || 0.1; // $0.10 per request max
    const costScore = Math.max(0, 100 - (metrics.cost / maxCost) * 100);
    score += costScore * costWeight;
    totalWeight += costWeight;

    // Experience bonus (more requests = more reliable data)
    const experienceWeight = 20;
    const experienceScore = Math.min(100, (metrics.totalRequests / 1000) * 100);
    score += experienceScore * experienceWeight;
    totalWeight += experienceWeight;

    return totalWeight > 0 ? score / totalWeight : 0;
  }

  async getProviderStats(organizationId: string): Promise<any> {
    const stats = {};
    
    for (const provider of this.providers.values()) {
      const metrics = await this.getProviderMetrics(provider.id);
      stats[provider.id] = {
        name: provider.name,
        metrics,
        models: await provider.getModels(),
      };
    }

    return stats;
  }

  registerProvider(provider: AIProvider): void {
    this.providers.set(provider.id, provider);
    this.logger.log(`Registered provider: ${provider.name}`);
  }

  getProvider(providerId: string): AIProvider | undefined {
    return this.providers.get(providerId);
  }

  getAllProviders(): AIProvider[] {
    return Array.from(this.providers.values());
  }
}
