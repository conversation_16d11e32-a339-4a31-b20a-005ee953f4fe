import { Controller, Get, Post, Body, Patch, Param, Delete, UseGuards, Request } from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { SessionsService } from './sessions.service';

@ApiTags('Sessions')
@Controller('api/sessions')
@UseGuards(AuthGuard('jwt'))
@ApiBearerAuth()
export class SessionsController {
  constructor(private readonly sessionsService: SessionsService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new session' })
  @ApiResponse({ status: 201, description: 'Session created successfully' })
  create(@Body() createSessionDto: any, @Request() req) {
    return this.sessionsService.create({
      ...createSessionDto,
      userId: req.user.id,
      organizationId: req.user.organizationId,
    });
  }

  @Get()
  @ApiOperation({ summary: 'Get all sessions in organization' })
  @ApiResponse({ status: 200, description: 'Sessions retrieved successfully' })
  findAll(@Request() req) {
    return this.sessionsService.findAll(req.user.organizationId);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get session by ID' })
  @ApiResponse({ status: 200, description: 'Session retrieved successfully' })
  findOne(@Param('id') id: string) {
    return this.sessionsService.findOne(id);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update session' })
  @ApiResponse({ status: 200, description: 'Session updated successfully' })
  update(@Param('id') id: string, @Body() updateSessionDto: any) {
    return this.sessionsService.update(id, updateSessionDto);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete session' })
  @ApiResponse({ status: 200, description: 'Session deleted successfully' })
  remove(@Param('id') id: string) {
    return this.sessionsService.remove(id);
  }

  @Patch(':id/memory')
  @ApiOperation({ summary: 'Update session memory' })
  @ApiResponse({ status: 200, description: 'Session memory updated successfully' })
  updateMemory(@Param('id') id: string, @Body('memory') memory: any) {
    return this.sessionsService.updateMemory(id, memory);
  }

  @Patch(':id/context')
  @ApiOperation({ summary: 'Update session context' })
  @ApiResponse({ status: 200, description: 'Session context updated successfully' })
  updateContext(@Param('id') id: string, @Body('context') context: any) {
    return this.sessionsService.updateContext(id, context);
  }

  @Post(':id/extend')
  @ApiOperation({ summary: 'Extend session expiration' })
  @ApiResponse({ status: 200, description: 'Session extended successfully' })
  extendSession(@Param('id') id: string, @Body('hours') hours?: number) {
    return this.sessionsService.extendSession(id, hours);
  }
}
