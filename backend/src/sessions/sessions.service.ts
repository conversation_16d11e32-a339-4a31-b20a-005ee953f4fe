import { Injectable } from '@nestjs/common';
import { PrismaService } from '../database/prisma.service';
import { RedisService } from '../redis/redis.service';

@Injectable()
export class SessionsService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly redisService: RedisService,
  ) {}

  async create(data: any) {
    const session = await this.prisma.session.create({
      data: {
        ...data,
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours
      },
      include: {
        user: true,
        agent: true,
        organization: true,
      },
    });

    // Store session in Redis for fast access
    await this.redisService.setSession(session.id, {
      id: session.id,
      userId: session.userId,
      agentId: session.agentId,
      organizationId: session.organizationId,
      context: session.context,
      memory: session.memory,
    });

    return session;
  }

  async findAll(organizationId: string) {
    return this.prisma.session.findMany({
      where: { organizationId },
      include: {
        user: true,
        agent: true,
      },
      orderBy: {
        createdAt: 'desc',
      },
    });
  }

  async findOne(id: string) {
    // Try Redis first for active sessions
    const cachedSession = await this.redisService.getSession(id);
    if (cachedSession) {
      return cachedSession;
    }

    // Fallback to database
    return this.prisma.session.findUnique({
      where: { id },
      include: {
        user: true,
        agent: true,
        organization: true,
      },
    });
  }

  async update(id: string, data: any) {
    const session = await this.prisma.session.update({
      where: { id },
      data,
      include: {
        user: true,
        agent: true,
        organization: true,
      },
    });

    // Update Redis cache
    await this.redisService.setSession(session.id, {
      id: session.id,
      userId: session.userId,
      agentId: session.agentId,
      organizationId: session.organizationId,
      context: session.context,
      memory: session.memory,
    });

    return session;
  }

  async remove(id: string) {
    // Remove from Redis
    await this.redisService.deleteSession(id);
    
    // Remove from database
    return this.prisma.session.delete({
      where: { id },
    });
  }

  async updateMemory(id: string, memory: any) {
    return this.update(id, { memory });
  }

  async updateContext(id: string, context: any) {
    return this.update(id, { context });
  }

  async extendSession(id: string, hours: number = 24) {
    const expiresAt = new Date(Date.now() + hours * 60 * 60 * 1000);
    
    // Extend in Redis
    await this.redisService.extendSession(id, hours * 60 * 60);
    
    // Extend in database
    return this.prisma.session.update({
      where: { id },
      data: { expiresAt },
    });
  }
}
