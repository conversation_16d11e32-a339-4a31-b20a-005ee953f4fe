import { Injectable } from '@nestjs/common';
import { PrismaService } from '../database/prisma.service';

@Injectable()
export class WorkflowsService {
  constructor(private readonly prisma: PrismaService) {}

  async create(data: any) {
    return this.prisma.workflow.create({
      data,
      include: {
        user: true,
        organization: true,
        steps: {
          include: {
            agent: true,
            tool: true,
          },
        },
      },
    });
  }

  async findAll(organizationId: string) {
    return this.prisma.workflow.findMany({
      where: { organizationId },
      include: {
        user: true,
        steps: {
          include: {
            agent: true,
            tool: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });
  }

  async findOne(id: string) {
    return this.prisma.workflow.findUnique({
      where: { id },
      include: {
        user: true,
        organization: true,
        steps: {
          include: {
            agent: true,
            tool: true,
          },
          orderBy: {
            order: 'asc',
          },
        },
        executions: {
          orderBy: {
            startedAt: 'desc',
          },
          take: 10,
        },
      },
    });
  }

  async update(id: string, data: any) {
    return this.prisma.workflow.update({
      where: { id },
      data,
      include: {
        user: true,
        organization: true,
        steps: {
          include: {
            agent: true,
            tool: true,
          },
        },
      },
    });
  }

  async remove(id: string) {
    return this.prisma.workflow.delete({
      where: { id },
    });
  }

  async createExecution(workflowId: string, input?: any) {
    return this.prisma.workflowExecution.create({
      data: {
        workflowId,
        input,
        status: 'PENDING',
      },
    });
  }

  async updateExecution(id: string, data: any) {
    return this.prisma.workflowExecution.update({
      where: { id },
      data,
    });
  }
}
