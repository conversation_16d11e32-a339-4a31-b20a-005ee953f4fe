import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../database/prisma.service';
import { RedisService } from '../redis/redis.service';
import { WebSocketGateway } from '../websocket/websocket.gateway';
import { ProvidersService } from '../providers/providers.service';
import { ToolExecutionService } from '../tools/tool-execution.service';
import { UAUICore } from '../uaui/uaui-core.service';

export interface WorkflowExecutionRequest {
  workflowId: string;
  userId: string;
  input?: Record<string, any>;
  context?: Record<string, any>;
  sessionId?: string;
}

export interface WorkflowExecutionResult {
  executionId: string;
  status: 'PENDING' | 'RUNNING' | 'COMPLETED' | 'FAILED' | 'CANCELLED';
  output?: any;
  error?: string;
  steps: StepExecutionResult[];
  startedAt: Date;
  completedAt?: Date;
  executionTime?: number;
}

export interface StepExecutionResult {
  stepId: string;
  type: 'AGENT' | 'TOOL' | 'CONDITION' | 'LOOP';
  status: 'PENDING' | 'RUNNING' | 'COMPLETED' | 'FAILED' | 'SKIPPED';
  input?: any;
  output?: any;
  error?: string;
  executionTime?: number;
  startedAt?: Date;
  completedAt?: Date;
}

@Injectable()
export class WorkflowExecutionService {
  private readonly logger = new Logger(WorkflowExecutionService.name);
  private readonly activeExecutions = new Map<string, WorkflowExecutionResult>();

  constructor(
    private readonly prisma: PrismaService,
    private readonly redisService: RedisService,
    private readonly websocketGateway: WebSocketGateway,
    private readonly providersService: ProvidersService,
    private readonly toolExecutionService: ToolExecutionService,
    private readonly uauiCore: UAUICore,
  ) {}

  async executeWorkflow(request: WorkflowExecutionRequest): Promise<WorkflowExecutionResult> {
    const executionId = `wf_exec_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const startTime = new Date();

    try {
      this.logger.log(`Starting workflow execution: ${request.workflowId} (${executionId})`);

      // Get workflow definition
      const workflow = await this.getWorkflow(request.workflowId);
      if (!workflow) {
        throw new Error(`Workflow not found: ${request.workflowId}`);
      }

      if (!workflow.isActive) {
        throw new Error(`Workflow is inactive: ${request.workflowId}`);
      }

      // Create execution record
      const execution = await this.createExecution(executionId, workflow, request, startTime);
      this.activeExecutions.set(executionId, execution);

      // Emit workflow started event
      this.websocketGateway.broadcastToUser(request.userId, {
        type: 'workflow_started',
        data: {
          executionId,
          workflowId: request.workflowId,
          workflowName: workflow.name,
        },
        timestamp: new Date().toISOString(),
      });

      // Execute workflow steps
      const result = await this.executeWorkflowSteps(execution, workflow, request);

      // Update final status
      result.status = result.error ? 'FAILED' : 'COMPLETED';
      result.completedAt = new Date();
      result.executionTime = result.completedAt.getTime() - result.startedAt.getTime();

      // Store execution result
      await this.storeExecutionResult(result);

      // Emit workflow completed event
      this.websocketGateway.broadcastToUser(request.userId, {
        type: result.status === 'COMPLETED' ? 'workflow_completed' : 'workflow_failed',
        data: {
          executionId,
          workflowId: request.workflowId,
          status: result.status,
          output: result.output,
          error: result.error,
          executionTime: result.executionTime,
        },
        timestamp: new Date().toISOString(),
      });

      this.logger.log(`Workflow execution ${result.status.toLowerCase()}: ${request.workflowId} (${result.executionTime}ms)`);
      return result;

    } catch (error) {
      const errorResult: WorkflowExecutionResult = {
        executionId,
        status: 'FAILED',
        error: error.message,
        steps: [],
        startedAt: startTime,
        completedAt: new Date(),
        executionTime: Date.now() - startTime.getTime(),
      };

      this.activeExecutions.set(executionId, errorResult);
      await this.storeExecutionResult(errorResult);

      this.websocketGateway.broadcastToUser(request.userId, {
        type: 'workflow_failed',
        data: {
          executionId,
          workflowId: request.workflowId,
          error: error.message,
        },
        timestamp: new Date().toISOString(),
      });

      this.logger.error(`Workflow execution failed: ${request.workflowId} - ${error.message}`);
      return errorResult;
    } finally {
      // Clean up active execution after delay
      setTimeout(() => {
        this.activeExecutions.delete(executionId);
      }, 60000); // 1 minute
    }
  }

  private async executeWorkflowSteps(
    execution: WorkflowExecutionResult,
    workflow: any,
    request: WorkflowExecutionRequest,
  ): Promise<WorkflowExecutionResult> {
    const context = {
      workflowInput: request.input || {},
      userContext: request.context || {},
      executionId: execution.executionId,
      userId: request.userId,
      sessionId: request.sessionId,
      variables: {},
    };

    execution.status = 'RUNNING';

    // Sort steps by order
    const sortedSteps = workflow.steps.sort((a, b) => a.order - b.order);

    for (const step of sortedSteps) {
      const stepResult = await this.executeStep(step, context, request);
      execution.steps.push(stepResult);

      // Update context with step output
      if (stepResult.output) {
        context.variables[`step_${step.order}_output`] = stepResult.output;
      }

      // Handle step failure
      if (stepResult.status === 'FAILED') {
        execution.error = `Step ${step.order} failed: ${stepResult.error}`;
        break;
      }

      // Handle conditional logic
      if (step.type === 'CONDITION' && !this.evaluateCondition(step, context)) {
        this.logger.log(`Condition step ${step.order} evaluated to false, skipping subsequent steps`);
        break;
      }

      // Emit step completed event
      this.websocketGateway.broadcastToUser(request.userId, {
        type: 'workflow_step_completed',
        data: {
          executionId: execution.executionId,
          stepId: step.id,
          stepOrder: step.order,
          stepType: step.type,
          status: stepResult.status,
          output: stepResult.output,
        },
        timestamp: new Date().toISOString(),
      });
    }

    // Set final output
    if (!execution.error && execution.steps.length > 0) {
      const lastStep = execution.steps[execution.steps.length - 1];
      execution.output = lastStep.output || context.variables;
    }

    return execution;
  }

  private async executeStep(
    step: any,
    context: any,
    request: WorkflowExecutionRequest,
  ): Promise<StepExecutionResult> {
    const stepResult: StepExecutionResult = {
      stepId: step.id,
      type: step.type,
      status: 'RUNNING',
      startedAt: new Date(),
    };

    try {
      this.logger.log(`Executing step ${step.order} (${step.type}): ${step.id}`);

      switch (step.type) {
        case 'AGENT':
          stepResult.output = await this.executeAgentStep(step, context, request);
          break;
        case 'TOOL':
          stepResult.output = await this.executeToolStep(step, context, request);
          break;
        case 'CONDITION':
          stepResult.output = this.evaluateCondition(step, context);
          break;
        case 'LOOP':
          stepResult.output = await this.executeLoopStep(step, context, request);
          break;
        default:
          throw new Error(`Unsupported step type: ${step.type}`);
      }

      stepResult.status = 'COMPLETED';
      stepResult.completedAt = new Date();
      stepResult.executionTime = stepResult.completedAt.getTime() - stepResult.startedAt.getTime();

    } catch (error) {
      stepResult.status = 'FAILED';
      stepResult.error = error.message;
      stepResult.completedAt = new Date();
      stepResult.executionTime = stepResult.completedAt.getTime() - stepResult.startedAt.getTime();
    }

    return stepResult;
  }

  private async executeAgentStep(step: any, context: any, request: WorkflowExecutionRequest): Promise<any> {
    if (!step.agent) {
      throw new Error(`Agent not found for step: ${step.id}`);
    }

    // Build agent prompt with context
    const prompt = this.interpolateTemplate(step.settings?.prompt || 'Process the following input:', context);
    const input = this.interpolateTemplate(JSON.stringify(context.workflowInput), context);

    // Execute through UAUI Core
    const uauiRequest = {
      userId: request.userId,
      sessionId: request.sessionId || `workflow_${context.executionId}`,
      message: `${prompt}\n\nInput: ${input}`,
      appType: 'dashboard' as const, // Use dashboard as closest match for workflow execution
      agentId: step.agent.id,
      context: {
        workflowContext: context,
        stepId: step.id,
      },
    };

    const response = await this.uauiCore.processAIRequest(uauiRequest);
    return response.final || response.chunks?.join('') || '';
  }

  private async executeToolStep(step: any, context: any, request: WorkflowExecutionRequest): Promise<any> {
    if (!step.tool) {
      throw new Error(`Tool not found for step: ${step.id}`);
    }

    // Build tool parameters from context
    const parameters = this.buildToolParameters(step.settings?.parameters || {}, context);

    const toolRequest = {
      toolId: step.tool.id,
      parameters,
      userId: request.userId,
      sessionId: request.sessionId,
      context: {
        workflowContext: context,
        stepId: step.id,
      },
    };

    const result = await this.toolExecutionService.executeTool(toolRequest);
    
    if (!result.success) {
      throw new Error(result.error || 'Tool execution failed');
    }

    return result.result;
  }

  private evaluateCondition(step: any, context: any): boolean {
    try {
      const condition = step.settings?.condition || 'true';
      const interpolatedCondition = this.interpolateTemplate(condition, context);
      
      // Simple condition evaluation (in production, use a safe expression evaluator)
      return new Function('context', `return ${interpolatedCondition}`)(context);
    } catch (error) {
      this.logger.warn(`Condition evaluation failed: ${error.message}`);
      return false;
    }
  }

  private async executeLoopStep(step: any, context: any, request: WorkflowExecutionRequest): Promise<any> {
    // Simplified loop implementation
    const iterations = step.settings?.iterations || 1;
    const results = [];

    for (let i = 0; i < iterations; i++) {
      const loopContext = { ...context, loopIndex: i };
      // Execute nested steps (simplified)
      results.push({ iteration: i, result: 'loop_executed' });
    }

    return results;
  }

  private interpolateTemplate(template: string, context: any): string {
    let result = template;
    
    // Replace context variables
    result = result.replace(/\$\{([^}]+)\}/g, (match, path) => {
      const value = this.getNestedValue(context, path);
      return value !== undefined ? String(value) : match;
    });

    return result;
  }

  private buildToolParameters(parameterTemplate: any, context: any): Record<string, any> {
    const parameters = {};
    
    for (const [key, value] of Object.entries(parameterTemplate)) {
      if (typeof value === 'string') {
        parameters[key] = this.interpolateTemplate(value, context);
      } else {
        parameters[key] = value;
      }
    }

    return parameters;
  }

  private getNestedValue(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => current?.[key], obj);
  }

  private async getWorkflow(workflowId: string): Promise<any> {
    return this.prisma.workflow.findUnique({
      where: { id: workflowId },
      include: {
        steps: {
          include: {
            agent: true,
            tool: true,
          },
          orderBy: {
            order: 'asc',
          },
        },
      },
    });
  }

  private async createExecution(
    executionId: string,
    workflow: any,
    request: WorkflowExecutionRequest,
    startTime: Date,
  ): Promise<WorkflowExecutionResult> {
    const execution: WorkflowExecutionResult = {
      executionId,
      status: 'PENDING',
      steps: [],
      startedAt: startTime,
    };

    // Store in database
    await this.prisma.workflowExecution.create({
      data: {
        id: executionId,
        workflowId: workflow.id,
        status: 'PENDING',
        input: request.input,
        startedAt: startTime,
      },
    });

    return execution;
  }

  private async storeExecutionResult(result: WorkflowExecutionResult): Promise<void> {
    await this.prisma.workflowExecution.update({
      where: { id: result.executionId },
      data: {
        status: result.status,
        output: result.output,
        error: result.error,
        completedAt: result.completedAt,
      },
    });
  }

  async getExecution(executionId: string): Promise<WorkflowExecutionResult | null> {
    return this.activeExecutions.get(executionId) || null;
  }

  async cancelExecution(executionId: string): Promise<void> {
    const execution = this.activeExecutions.get(executionId);
    if (execution) {
      execution.status = 'CANCELLED';
      execution.completedAt = new Date();
      await this.storeExecutionResult(execution);
    }
  }
}
