import { Controller, Get, Post, Body, Patch, Param, Delete, UseGuards, Request } from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { WorkflowsService } from './workflows.service';
import { WorkflowExecutionService } from './workflow-execution.service';

@ApiTags('Workflows')
@Controller('api/workflows')
@UseGuards(AuthGuard('jwt'))
@ApiBearerAuth()
export class WorkflowsController {
  constructor(
    private readonly workflowsService: WorkflowsService,
    private readonly workflowExecutionService: WorkflowExecutionService,
  ) {}

  @Post()
  @ApiOperation({ summary: 'Create a new workflow' })
  @ApiResponse({ status: 201, description: 'Workflow created successfully' })
  create(@Body() createWorkflowDto: any, @Request() req) {
    return this.workflowsService.create({
      ...createWorkflowDto,
      userId: req.user.id,
      organizationId: req.user.organizationId,
    });
  }

  @Get()
  @ApiOperation({ summary: 'Get all workflows in organization' })
  @ApiResponse({ status: 200, description: 'Workflows retrieved successfully' })
  findAll(@Request() req) {
    return this.workflowsService.findAll(req.user.organizationId);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get workflow by ID' })
  @ApiResponse({ status: 200, description: 'Workflow retrieved successfully' })
  findOne(@Param('id') id: string) {
    return this.workflowsService.findOne(id);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update workflow' })
  @ApiResponse({ status: 200, description: 'Workflow updated successfully' })
  update(@Param('id') id: string, @Body() updateWorkflowDto: any) {
    return this.workflowsService.update(id, updateWorkflowDto);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete workflow' })
  @ApiResponse({ status: 200, description: 'Workflow deleted successfully' })
  remove(@Param('id') id: string) {
    return this.workflowsService.remove(id);
  }

  @Post(':id/execute')
  @ApiOperation({ summary: 'Execute workflow' })
  @ApiResponse({ status: 201, description: 'Workflow execution started' })
  async execute(
    @Param('id') workflowId: string,
    @Body() body: { input?: any; context?: any; sessionId?: string },
    @Request() req,
  ) {
    return this.workflowExecutionService.executeWorkflow({
      workflowId,
      userId: req.user.id,
      input: body.input,
      context: body.context,
      sessionId: body.sessionId,
    });
  }

  @Get('executions/:executionId')
  @ApiOperation({ summary: 'Get workflow execution status' })
  @ApiResponse({ status: 200, description: 'Execution status retrieved' })
  async getExecution(@Param('executionId') executionId: string) {
    return this.workflowExecutionService.getExecution(executionId);
  }

  @Post('executions/:executionId/cancel')
  @ApiOperation({ summary: 'Cancel workflow execution' })
  @ApiResponse({ status: 200, description: 'Execution cancelled' })
  async cancelExecution(@Param('executionId') executionId: string) {
    await this.workflowExecutionService.cancelExecution(executionId);
    return { success: true };
  }
}
