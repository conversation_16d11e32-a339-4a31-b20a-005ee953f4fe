import { Module } from '@nestjs/common';
import { WorkflowsService } from './workflows.service';
import { WorkflowsController } from './workflows.controller';
import { WorkflowExecutionService } from './workflow-execution.service';

@Module({
  providers: [WorkflowsService, WorkflowExecutionService],
  controllers: [WorkflowsController],
  exports: [WorkflowsService, WorkflowExecutionService],
})
export class WorkflowsModule {}
