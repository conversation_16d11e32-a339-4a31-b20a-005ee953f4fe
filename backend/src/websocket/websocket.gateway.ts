// @ts-nocheck
import {
  WebSocketGateway as WSGateway,
  WebSocketServer,
  SubscribeMessage,
  OnGatewayConnection,
  OnGatewayDisconnect,
  MessageBody,
  ConnectedSocket,
} from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';
import { Logger } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';

import { RedisService } from '../redis/redis.service';

interface WebSocketMessage {
  type: string;
  data: any;
  timestamp: string;
}

interface AuthenticatedSocket extends Socket {
  userId?: string;
  organizationId?: string;
}

@WSGateway({
  cors: {
    origin: process.env.FRONTEND_URL || 'http://localhost:3000',
    credentials: true,
  },
  path: '/ws',
})
export class WebSocketGateway implements OnGatewayConnection, OnGatewayDisconnect {
  @WebSocketServer()
  server!: Server;

  private readonly logger = new Logger(WebSocketGateway.name);
  private connectedClients = new Map<string, AuthenticatedSocket>();

  constructor(
    private readonly jwtService: JwtService,
    private readonly redisService: RedisService,
  ) {}

  async handleConnection(client: AuthenticatedSocket) {
    try {
      // Extract token from handshake
      const token = client.handshake.auth?.token || client.handshake.headers?.authorization?.replace('Bearer ', '');
      
      if (!token) {
        this.logger.warn(`Client ${client.id} connected without token`);
        client.disconnect();
        return;
      }

      // Verify JWT token
      const payload = this.jwtService.verify(token);
      client.userId = payload.sub;
      client.organizationId = payload.organizationId;

      // Store client connection
      this.connectedClients.set(client.id, client);

      // Join organization room
      client.join(`org:${client.organizationId}`);
      client.join(`user:${client.userId}`);

      this.logger.log(`Client ${client.id} connected (User: ${client.userId})`);

      // Notify organization about user joining
      if (client.organizationId) {
        this.broadcastToOrganization(client.organizationId, {
          type: 'user.joined',
          data: {
            userId: client.userId,
            socketId: client.id,
          },
          timestamp: new Date().toISOString(),
        });
      }

    } catch (error) {
      this.logger.error(`Authentication failed for client ${client.id}:`, error instanceof Error ? error.message : 'Unknown error');
      client.disconnect();
    }
  }

  handleDisconnect(client: AuthenticatedSocket) {
    this.connectedClients.delete(client.id);
    
    if (client.userId && client.organizationId) {
      this.logger.log(`Client ${client.id} disconnected (User: ${client.userId})`);
      
      // Notify organization about user leaving
      this.broadcastToOrganization(client.organizationId, {
        type: 'user.left',
        data: {
          userId: client.userId,
          socketId: client.id,
        },
        timestamp: new Date().toISOString(),
      });
    }
  }

  // APIX Protocol Event Handlers
  @SubscribeMessage('user_message')
  async handleUserMessage(
    @ConnectedSocket() client: AuthenticatedSocket,
    @MessageBody() data: any,
  ) {
    this.logger.log(`User message from ${client.userId}:`, data);
    
    // Broadcast to organization
    if (client.organizationId) {
      this.broadcastToOrganization(client.organizationId, {
        type: 'user_message',
        data: {
          userId: client.userId,
          message: data.message,
          sessionId: data.sessionId,
        },
        timestamp: new Date().toISOString(),
      });
    }
  }

  @SubscribeMessage('agent_response')
  async handleAgentResponse(
    @ConnectedSocket() client: AuthenticatedSocket,
    @MessageBody() data: any,
  ) {
    this.broadcastToUser(data.userId || client.userId, {
      type: 'agent_response',
      data,
      timestamp: new Date().toISOString(),
    });
  }

  @SubscribeMessage('thinking_status')
  async handleThinkingStatus(
    @ConnectedSocket() client: AuthenticatedSocket,
    @MessageBody() data: any,
  ) {
    this.broadcastToUser(data.userId || client.userId, {
      type: 'thinking_status',
      data,
      timestamp: new Date().toISOString(),
    });
  }

  @SubscribeMessage('text_chunk')
  async handleTextChunk(
    @ConnectedSocket() client: AuthenticatedSocket,
    @MessageBody() data: any,
  ) {
    this.broadcastToUser(data.userId || client.userId, {
      type: 'text_chunk',
      data,
      timestamp: new Date().toISOString(),
    });
  }

  @SubscribeMessage('tool_call_start')
  async handleToolCallStart(
    @ConnectedSocket() client: AuthenticatedSocket,
    @MessageBody() data: any,
  ) {
    this.broadcastToUser(data.userId || client.userId, {
      type: 'tool_call_start',
      data,
      timestamp: new Date().toISOString(),
    });
  }

  @SubscribeMessage('tool_call_result')
  async handleToolCallResult(
    @ConnectedSocket() client: AuthenticatedSocket,
    @MessageBody() data: any,
  ) {
    this.broadcastToUser(data.userId || client.userId, {
      type: 'tool_call_result',
      data,
      timestamp: new Date().toISOString(),
    });
  }

  @SubscribeMessage('tool_call_error')
  async handleToolCallError(
    @ConnectedSocket() client: AuthenticatedSocket,
    @MessageBody() data: any,
  ) {
    this.broadcastToUser(data.userId || client.userId, {
      type: 'tool_call_error',
      data,
      timestamp: new Date().toISOString(),
    });
  }

  @SubscribeMessage('request_user_input')
  async handleRequestUserInput(
    @ConnectedSocket() client: AuthenticatedSocket,
    @MessageBody() data: any,
  ) {
    this.broadcastToUser(data.userId || client.userId, {
      type: 'request_user_input',
      data,
      timestamp: new Date().toISOString(),
    });
  }

  @SubscribeMessage('user_response')
  async handleUserResponse(
    @ConnectedSocket() client: AuthenticatedSocket,
    @MessageBody() data: any,
  ) {
    // Store user response and notify relevant services
    await this.redisService.set(
      `user_response:${data.sessionId}:${data.requestId}`,
      data.response,
      300 // 5 minutes TTL
    );

    if (client.organizationId) {
      this.broadcastToOrganization(client.organizationId, {
        type: 'user_response',
        data: {
          ...data,
          userId: client.userId,
        },
        timestamp: new Date().toISOString(),
      });
    }
  }

  @SubscribeMessage('state_update')
  async handleStateUpdate(
    @ConnectedSocket() client: AuthenticatedSocket,
    @MessageBody() data: any,
  ) {
    // Store state update in Redis
    if (client.organizationId) {
      await this.redisService.setState(
        `${client.organizationId}:${data.key}`,
        data.state,
        data.ttl || 1800
      );

      this.broadcastToOrganization(client.organizationId, {
        type: 'state_update',
        data,
        timestamp: new Date().toISOString(),
      });
    }
  }

  @SubscribeMessage('workflow_completed')
  async handleWorkflowCompleted(
    @ConnectedSocket() client: AuthenticatedSocket,
    @MessageBody() data: any,
  ) {
    if (client.organizationId) {
      this.broadcastToOrganization(client.organizationId, {
        type: 'workflow_completed',
        data,
        timestamp: new Date().toISOString(),
      });
    }
  }

  @SubscribeMessage('workflow_failed')
  async handleWorkflowFailed(
    @ConnectedSocket() client: AuthenticatedSocket,
    @MessageBody() data: any,
  ) {
    if (client.organizationId) {
      this.broadcastToOrganization(client.organizationId, {
        type: 'workflow_failed',
        data,
        timestamp: new Date().toISOString(),
      });
    }
  }

  // Utility methods for broadcasting
  broadcastToUser(userId: string, message: WebSocketMessage) {
    this.server.to(`user:${userId}`).emit('message', message);
  }

  broadcastToOrganization(organizationId: string, message: WebSocketMessage) {
    this.server.to(`org:${organizationId}`).emit('message', message);
  }

  broadcastToAll(message: WebSocketMessage) {
    this.server.emit('message', message);
  }

  // Get connected users for an organization
  getConnectedUsers(organizationId: string): string[] {
    const users: string[] = [];
    this.connectedClients.forEach((client) => {
      if (client.organizationId === organizationId && client.userId) {
        users.push(client.userId);
      }
    });
    return Array.from(new Set(users)); // Remove duplicates
  }
}
