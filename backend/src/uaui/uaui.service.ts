import { Injectable } from '@nestjs/common';
import { UAUICore } from './uaui-core.service';
import { EventBusService } from './event-bus.service';
import { StateManagerService } from './state-manager.service';
import { RouterEngineService } from './router-engine.service';

@Injectable()
export class UAUIService {
  constructor(
    private readonly uauiCore: UAUICore,
    private readonly eventBus: EventBusService,
    private readonly stateManager: StateManagerService,
    private readonly routerEngine: RouterEngineService,
  ) {}

  async healthCheck(): Promise<{ status: string; timestamp: string }> {
    return {
      status: 'healthy',
      timestamp: new Date().toISOString(),
    };
  }

  async getStats(): Promise<any> {
    return {
      activeConnections: 0,
      eventsProcessed: 0,
      stateUpdates: 0,
      routingRules: this.routerEngine.getAllRoutingRules().length,
    };
  }
}
