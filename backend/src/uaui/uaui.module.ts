import { Module } from '@nestjs/common';
import { UAUICore } from './uaui-core.service';
import { EventBusService } from './event-bus.service';
import { StateManagerService } from './state-manager.service';
import { RouterEngineService } from './router-engine.service';
import { UAUIService } from './uaui.service';
import { UAUIController } from './uaui.controller';

@Module({
  providers: [
    UAUICore,
    EventBusService,
    StateManagerService,
    RouterEngineService,
    UAUIService,
  ],
  controllers: [UAUIController],
  exports: [
    UAUICore,
    EventBusService,
    StateManagerService,
    RouterEngineService,
    UAUIService,
  ],
})
export class UAUIModule {}
