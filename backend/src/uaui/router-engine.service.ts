import { Injectable, Logger } from '@nestjs/common';
import { EventBusService } from './event-bus.service';
import { StateManagerService } from './state-manager.service';

export interface RouterCommand {
  type: 'inject_dom_instruction' | 'route_to_dashboard' | 'open_modal' | 'show_notification' | 'update_ui' | 'redirect';
  target: string;
  payload: any;
  userId?: string;
  organizationId?: string;
  metadata?: Record<string, any>;
}

export interface RoutingRule {
  id: string;
  condition: (context: any) => boolean;
  action: RouterCommand;
  priority: number;
  isActive: boolean;
}

@Injectable()
export class RouterEngineService {
  private readonly logger = new Logger(RouterEngineService.name);
  private readonly routingRules = new Map<string, RoutingRule>();

  constructor(
    private readonly eventBus: EventBusService,
    private readonly stateManager: StateManagerService,
  ) {
    this.initializeDefaultRules();
  }

  async executeCommand(command: RouterCommand): Promise<void> {
    try {
      this.logger.debug(`Executing router command: ${command.type}`);

      switch (command.type) {
        case 'inject_dom_instruction':
          await this.injectDOMInstruction(command);
          break;
        case 'route_to_dashboard':
          await this.routeToDashboard(command);
          break;
        case 'open_modal':
          await this.openModal(command);
          break;
        case 'show_notification':
          await this.showNotification(command);
          break;
        case 'update_ui':
          await this.updateUI(command);
          break;
        case 'redirect':
          await this.redirect(command);
          break;
        default:
          this.logger.warn(`Unknown router command type: ${command.type}`);
      }

      // Emit command execution event
      await this.eventBus.emitEvent('router.command.executed', {
        command,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      this.logger.error(`Failed to execute router command: ${error.message}`);
      throw error;
    }
  }

  async evaluateRules(context: any): Promise<RouterCommand[]> {
    const commands: RouterCommand[] = [];

    try {
      // Sort rules by priority (higher priority first)
      const sortedRules = Array.from(this.routingRules.values())
        .filter(rule => rule.isActive)
        .sort((a, b) => b.priority - a.priority);

      for (const rule of sortedRules) {
        try {
          if (rule.condition(context)) {
            commands.push(rule.action);
            this.logger.debug(`Routing rule triggered: ${rule.id}`);
          }
        } catch (error) {
          this.logger.error(`Error evaluating rule ${rule.id}: ${error.message}`);
        }
      }
    } catch (error) {
      this.logger.error(`Failed to evaluate routing rules: ${error.message}`);
    }

    return commands;
  }

  addRoutingRule(rule: RoutingRule): void {
    this.routingRules.set(rule.id, rule);
    this.logger.log(`Added routing rule: ${rule.id}`);
  }

  removeRoutingRule(ruleId: string): void {
    this.routingRules.delete(ruleId);
    this.logger.log(`Removed routing rule: ${ruleId}`);
  }

  updateRoutingRule(ruleId: string, updates: Partial<RoutingRule>): void {
    const existingRule = this.routingRules.get(ruleId);
    if (existingRule) {
      this.routingRules.set(ruleId, { ...existingRule, ...updates });
      this.logger.log(`Updated routing rule: ${ruleId}`);
    }
  }

  getRoutingRule(ruleId: string): RoutingRule | undefined {
    return this.routingRules.get(ruleId);
  }

  getAllRoutingRules(): RoutingRule[] {
    return Array.from(this.routingRules.values());
  }

  async processContextChange(context: any): Promise<void> {
    try {
      const commands = await this.evaluateRules(context);
      
      for (const command of commands) {
        await this.executeCommand(command);
      }
    } catch (error) {
      this.logger.error(`Failed to process context change: ${error.message}`);
    }
  }

  private async injectDOMInstruction(command: RouterCommand): Promise<void> {
    await this.eventBus.emitEvent('ui.dom.inject', {
      target: command.target,
      instruction: command.payload.instruction,
      selector: command.payload.selector,
      userId: command.userId,
    });
  }

  private async routeToDashboard(command: RouterCommand): Promise<void> {
    await this.eventBus.emitEvent('ui.route.change', {
      route: '/dashboard',
      params: command.payload.params,
      userId: command.userId,
    });
  }

  private async openModal(command: RouterCommand): Promise<void> {
    await this.eventBus.emitEvent('ui.modal.open', {
      modalId: command.payload.modalId,
      title: command.payload.title,
      content: command.payload.content,
      size: command.payload.size || 'medium',
      userId: command.userId,
    });
  }

  private async showNotification(command: RouterCommand): Promise<void> {
    await this.eventBus.emitEvent('ui.notification.show', {
      type: command.payload.type || 'info',
      title: command.payload.title,
      message: command.payload.message,
      duration: command.payload.duration || 5000,
      userId: command.userId,
    });
  }

  private async updateUI(command: RouterCommand): Promise<void> {
    await this.eventBus.emitEvent('ui.update', {
      component: command.target,
      updates: command.payload.updates,
      userId: command.userId,
    });
  }

  private async redirect(command: RouterCommand): Promise<void> {
    await this.eventBus.emitEvent('ui.redirect', {
      url: command.payload.url,
      newTab: command.payload.newTab || false,
      userId: command.userId,
    });
  }

  private initializeDefaultRules(): void {
    // Rule: Show welcome message for new users
    this.addRoutingRule({
      id: 'welcome_new_user',
      condition: (ctx) => ctx.isNewUser === true,
      action: {
        type: 'show_notification',
        target: 'notification',
        payload: {
          type: 'success',
          title: 'Welcome to SynapseAI!',
          message: 'Get started by creating your first AI agent.',
          duration: 8000,
        },
      },
      priority: 100,
      isActive: true,
    });

    // Rule: Redirect to dashboard after successful login
    this.addRoutingRule({
      id: 'post_login_redirect',
      condition: (ctx) => ctx.event === 'user.login.success',
      action: {
        type: 'route_to_dashboard',
        target: 'main',
        payload: {
          params: { tab: 'agents' },
        },
      },
      priority: 90,
      isActive: true,
    });

    // Rule: Show error modal for critical errors
    this.addRoutingRule({
      id: 'critical_error_modal',
      condition: (ctx) => ctx.errorLevel === 'critical',
      action: {
        type: 'open_modal',
        target: 'error-modal',
        payload: {
          modalId: 'critical-error',
          title: 'Critical Error',
          content: 'A critical error has occurred. Please contact support.',
          size: 'large',
        },
      },
      priority: 200,
      isActive: true,
    });

    // Rule: Update UI when agent status changes
    this.addRoutingRule({
      id: 'agent_status_update',
      condition: (ctx) => ctx.event === 'agent.status.changed',
      action: {
        type: 'update_ui',
        target: 'agent-status',
        payload: {
          updates: {
            status: 'updated',
            lastActivity: new Date().toISOString(),
          },
        },
      },
      priority: 50,
      isActive: true,
    });

    // Rule: Inject DOM instruction for widget customization
    this.addRoutingRule({
      id: 'widget_customization',
      condition: (ctx) => ctx.event === 'widget.customize' && ctx.appType === 'widget',
      action: {
        type: 'inject_dom_instruction',
        target: 'widget-container',
        payload: {
          instruction: 'updateTheme',
          selector: '.widget-container',
          data: { theme: 'default' },
        },
      },
      priority: 70,
      isActive: true,
    });

    this.logger.log('Initialized default routing rules');
  }

  // Advanced routing features
  async createConditionalRoute(
    condition: string,
    action: RouterCommand,
    priority: number = 50,
  ): Promise<string> {
    const ruleId = `conditional_${Date.now()}`;
    
    // Parse condition string into function (simplified implementation)
    const conditionFn = new Function('ctx', `return ${condition}`) as (context: any) => boolean;
    
    this.addRoutingRule({
      id: ruleId,
      condition: conditionFn,
      action,
      priority,
      isActive: true,
    });

    return ruleId;
  }

  async enableRule(ruleId: string): Promise<void> {
    this.updateRoutingRule(ruleId, { isActive: true });
  }

  async disableRule(ruleId: string): Promise<void> {
    this.updateRoutingRule(ruleId, { isActive: false });
  }

  async getRouterStats(): Promise<any> {
    return {
      totalRules: this.routingRules.size,
      activeRules: Array.from(this.routingRules.values()).filter(r => r.isActive).length,
      rulesByPriority: Array.from(this.routingRules.values())
        .reduce((acc, rule) => {
          acc[rule.priority] = (acc[rule.priority] || 0) + 1;
          return acc;
        }, {}),
    };
  }
}
