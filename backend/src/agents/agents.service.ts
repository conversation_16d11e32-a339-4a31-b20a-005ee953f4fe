import { Injectable } from '@nestjs/common';
import { PrismaService } from '../database/prisma.service';

@Injectable()
export class AgentsService {
  constructor(private readonly prisma: PrismaService) {}

  async create(data: any) {
    return this.prisma.agent.create({
      data,
      include: {
        user: true,
        organization: true,
      },
    });
  }

  async findAll(organizationId: string) {
    return this.prisma.agent.findMany({
      where: { organizationId },
      include: {
        user: true,
      },
      orderBy: {
        createdAt: 'desc',
      },
    });
  }

  async findOne(id: string) {
    return this.prisma.agent.findUnique({
      where: { id },
      include: {
        user: true,
        organization: true,
      },
    });
  }

  async update(id: string, data: any) {
    return this.prisma.agent.update({
      where: { id },
      data,
      include: {
        user: true,
        organization: true,
      },
    });
  }

  async remove(id: string) {
    return this.prisma.agent.delete({
      where: { id },
    });
  }
}
