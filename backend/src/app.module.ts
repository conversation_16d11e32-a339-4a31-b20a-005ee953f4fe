import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { ThrottlerModule } from '@nestjs/throttler';
import { ServeStaticModule } from '@nestjs/serve-static';
import { join } from 'path';

import { AuthModule } from './auth/auth.module';
import { UsersModule } from './users/users.module';
import { AgentsModule } from './agents/agents.module';
import { ToolsModule } from './tools/tools.module';
import { WorkflowsModule } from './workflows/workflows.module';
import { ProvidersModule } from './providers/providers.module';
import { SessionsModule } from './sessions/sessions.module';
import { WebSocketModule } from './websocket/websocket.module';
import { DatabaseModule } from './database/database.module';
import { RedisModule } from './redis/redis.module';
import { UAUIModule } from './uaui/uaui.module';

@Module({
  imports: [
    // Configuration
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env.local', '.env'],
    }),
    
    // Rate limiting
    ThrottlerModule.forRoot([
      {
        ttl: 60000, // 1 minute
        limit: 100, // 100 requests per minute
      },
    ]),
    
    // Serve static files (for frontend)
    ServeStaticModule.forRoot({
      rootPath: join(__dirname, '..', '..', 'dist'),
      exclude: ['/api*'],
    }),
    
    // Core modules
    DatabaseModule,
    RedisModule,
    AuthModule,
    UsersModule,
    AgentsModule,
    ToolsModule,
    WorkflowsModule,
    ProvidersModule,
    SessionsModule,
    WebSocketModule,
    UAUIModule,
  ],
})
export class AppModule {}
