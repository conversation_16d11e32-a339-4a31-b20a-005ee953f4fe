import { Controller, Get, Post, Body, Patch, Param, Delete, UseGuards, Request } from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { ToolsService } from './tools.service';
import { ToolExecutionService } from './tool-execution.service';

@ApiTags('Tools')
@Controller('api/tools')
@UseGuards(AuthGuard('jwt'))
@ApiBearerAuth()
export class ToolsController {
  constructor(
    private readonly toolsService: ToolsService,
    private readonly toolExecutionService: ToolExecutionService,
  ) {}

  @Post()
  @ApiOperation({ summary: 'Create a new tool' })
  @ApiResponse({ status: 201, description: 'Tool created successfully' })
  create(@Body() createToolDto: any, @Request() req) {
    return this.toolsService.create({
      ...createToolDto,
      userId: req.user.id,
      organizationId: req.user.organizationId,
    });
  }

  @Get()
  @ApiOperation({ summary: 'Get all tools in organization' })
  @ApiResponse({ status: 200, description: 'Tools retrieved successfully' })
  findAll(@Request() req) {
    return this.toolsService.findAll(req.user.organizationId);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get tool by ID' })
  @ApiResponse({ status: 200, description: 'Tool retrieved successfully' })
  findOne(@Param('id') id: string) {
    return this.toolsService.findOne(id);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update tool' })
  @ApiResponse({ status: 200, description: 'Tool updated successfully' })
  update(@Param('id') id: string, @Body() updateToolDto: any) {
    return this.toolsService.update(id, updateToolDto);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete tool' })
  @ApiResponse({ status: 200, description: 'Tool deleted successfully' })
  remove(@Param('id') id: string) {
    return this.toolsService.remove(id);
  }

  @Post(':id/execute')
  @ApiOperation({ summary: 'Execute tool' })
  @ApiResponse({ status: 200, description: 'Tool executed successfully' })
  async executeTool(
    @Param('id') toolId: string,
    @Body() body: { parameters: Record<string, any>; sessionId?: string },
    @Request() req,
  ) {
    return this.toolExecutionService.executeTool({
      toolId,
      parameters: body.parameters,
      userId: req.user.id,
      sessionId: body.sessionId,
    });
  }

  @Get(':id/stats')
  @ApiOperation({ summary: 'Get tool execution statistics' })
  @ApiResponse({ status: 200, description: 'Tool stats retrieved successfully' })
  async getToolStats(@Param('id') toolId: string, @Request() req) {
    return this.toolExecutionService.getToolExecutionStats(toolId, req.user.organizationId);
  }
}
