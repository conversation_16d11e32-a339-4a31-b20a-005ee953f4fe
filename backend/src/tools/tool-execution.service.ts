import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../database/prisma.service';
import { RedisService } from '../redis/redis.service';
import { WebSocketGateway } from '../websocket/websocket.gateway';
import { z } from 'zod';

export interface ToolExecutionRequest {
  toolId: string;
  parameters: Record<string, any>;
  userId: string;
  sessionId?: string;
  timeout?: number;
  retryCount?: number;
  context?: Record<string, any>;
}

export interface ToolExecutionResult {
  success: boolean;
  result?: any;
  error?: string;
  executionTime: number;
  retryCount: number;
  metadata?: Record<string, any>;
}

export interface ToolExecutionContext {
  toolId: string;
  userId: string;
  sessionId?: string;
  organizationId: string;
  startTime: number;
  attempt: number;
  maxRetries: number;
}

@Injectable()
export class ToolExecutionService {
  private readonly logger = new Logger(ToolExecutionService.name);
  private readonly executionCache = new Map<string, ToolExecutionResult>();

  constructor(
    private readonly prisma: PrismaService,
    private readonly redisService: RedisService,
    private readonly websocketGateway: WebSocketGateway,
  ) {}

  async executeTool(request: ToolExecutionRequest): Promise<ToolExecutionResult> {
    const executionId = `exec_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const startTime = Date.now();

    try {
      this.logger.log(`Starting tool execution: ${request.toolId} (${executionId})`);

      // Emit tool call start event
      this.websocketGateway.broadcastToUser(request.userId, {
        type: 'tool_call_start',
        data: {
          toolId: request.toolId,
          executionId,
          parameters: request.parameters,
        },
        timestamp: new Date().toISOString(),
      });

      // Get tool configuration
      const tool = await this.getTool(request.toolId);
      if (!tool) {
        throw new Error(`Tool not found: ${request.toolId}`);
      }

      if (!tool.isActive) {
        throw new Error(`Tool is inactive: ${request.toolId}`);
      }

      // Validate input parameters
      await this.validateInput(tool, request.parameters);

      // Create execution context
      const context: ToolExecutionContext = {
        toolId: request.toolId,
        userId: request.userId,
        sessionId: request.sessionId,
        organizationId: tool.organizationId,
        startTime,
        attempt: 1,
        maxRetries: request.retryCount || tool.retryCount || 3,
      };

      // Execute with retry logic
      const result = await this.executeWithRetry(tool, request, context);

      // Cache successful results
      if (result.success) {
        this.cacheResult(executionId, result);
      }

      // Emit tool call result event
      this.websocketGateway.broadcastToUser(request.userId, {
        type: 'tool_call_result',
        data: {
          toolId: request.toolId,
          executionId,
          result: result.result,
          success: result.success,
          executionTime: result.executionTime,
        },
        timestamp: new Date().toISOString(),
      });

      this.logger.log(`Tool execution completed: ${request.toolId} (${result.executionTime}ms)`);
      return result;

    } catch (error) {
      const executionTime = Date.now() - startTime;
      const errorResult: ToolExecutionResult = {
        success: false,
        error: error.message,
        executionTime,
        retryCount: 0,
      };

      // Emit tool call error event
      this.websocketGateway.broadcastToUser(request.userId, {
        type: 'tool_call_error',
        data: {
          toolId: request.toolId,
          executionId,
          error: error.message,
          executionTime,
        },
        timestamp: new Date().toISOString(),
      });

      this.logger.error(`Tool execution failed: ${request.toolId} - ${error.message}`);
      return errorResult;
    }
  }

  private async executeWithRetry(
    tool: any,
    request: ToolExecutionRequest,
    context: ToolExecutionContext,
  ): Promise<ToolExecutionResult> {
    let lastError: Error;

    for (let attempt = 1; attempt <= context.maxRetries; attempt++) {
      try {
        context.attempt = attempt;
        
        if (attempt > 1) {
          const delay = this.calculateRetryDelay(attempt);
          this.logger.log(`Retrying tool execution (attempt ${attempt}/${context.maxRetries}) after ${delay}ms`);
          await this.sleep(delay);
        }

        const result = await this.executeToolLogic(tool, request, context);
        
        return {
          success: true,
          result,
          executionTime: Date.now() - context.startTime,
          retryCount: attempt - 1,
        };

      } catch (error) {
        lastError = error;
        this.logger.warn(`Tool execution attempt ${attempt} failed: ${error.message}`);
        
        // Don't retry for certain types of errors
        if (this.isNonRetryableError(error)) {
          break;
        }
      }
    }

    return {
      success: false,
      error: lastError.message,
      executionTime: Date.now() - context.startTime,
      retryCount: context.maxRetries,
    };
  }

  private async executeToolLogic(
    tool: any,
    request: ToolExecutionRequest,
    context: ToolExecutionContext,
  ): Promise<any> {
    switch (tool.type) {
      case 'API':
        return this.executeApiTool(tool, request, context);
      case 'WEBHOOK':
        return this.executeWebhookTool(tool, request, context);
      case 'FUNCTION':
        return this.executeFunctionTool(tool, request, context);
      case 'DATABASE':
        return this.executeDatabaseTool(tool, request, context);
      default:
        throw new Error(`Unsupported tool type: ${tool.type}`);
    }
  }

  private async executeApiTool(tool: any, request: ToolExecutionRequest, context: ToolExecutionContext): Promise<any> {
    const url = this.interpolateUrl(tool.endpoint, request.parameters);
    const headers = this.buildHeaders(tool);
    const timeout = request.timeout || tool.timeout || 30000;

    const fetchOptions: RequestInit = {
      method: tool.method || 'POST',
      headers,
      signal: AbortSignal.timeout(timeout),
    };

    // Add body for POST/PUT/PATCH requests
    if (['POST', 'PUT', 'PATCH'].includes(tool.method)) {
      fetchOptions.body = JSON.stringify(request.parameters);
    }

    const response = await fetch(url, fetchOptions);

    if (!response.ok) {
      throw new Error(`API request failed: ${response.status} ${response.statusText}`);
    }

    const contentType = response.headers.get('content-type');
    if (contentType?.includes('application/json')) {
      return await response.json();
    } else {
      return await response.text();
    }
  }

  private async executeWebhookTool(tool: any, request: ToolExecutionRequest, context: ToolExecutionContext): Promise<any> {
    // Similar to API tool but with webhook-specific logic
    return this.executeApiTool(tool, request, context);
  }

  private async executeFunctionTool(tool: any, request: ToolExecutionRequest, context: ToolExecutionContext): Promise<any> {
    // Execute custom function tools (would require sandboxed execution in production)
    throw new Error('Function tools not implemented yet');
  }

  private async executeDatabaseTool(tool: any, request: ToolExecutionRequest, context: ToolExecutionContext): Promise<any> {
    // Execute database queries (would require proper query validation and sandboxing)
    throw new Error('Database tools not implemented yet');
  }

  private async getTool(toolId: string): Promise<any> {
    return this.prisma.tool.findUnique({
      where: { id: toolId },
    });
  }

  private async validateInput(tool: any, parameters: Record<string, any>): Promise<void> {
    try {
      if (tool.inputSchema) {
        const schema = z.object(this.convertJsonSchemaToZod(tool.inputSchema));
        schema.parse(parameters);
      }
    } catch (error) {
      throw new Error(`Input validation failed: ${error.message}`);
    }
  }

  private convertJsonSchemaToZod(jsonSchema: any): Record<string, any> {
    const zodSchema: Record<string, any> = {};

    if (jsonSchema.properties) {
      for (const [key, prop] of Object.entries(jsonSchema.properties as any)) {
        zodSchema[key] = this.convertJsonSchemaPropertyToZod(prop);
      }
    }

    return zodSchema;
  }

  private convertJsonSchemaPropertyToZod(property: any): any {
    switch (property.type) {
      case 'string':
        return z.string();
      case 'number':
        return z.number();
      case 'boolean':
        return z.boolean();
      case 'array':
        return z.array(z.any());
      case 'object':
        return z.object({});
      default:
        return z.any();
    }
  }

  private buildHeaders(tool: any): Record<string, string> {
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      'User-Agent': 'SynapseAI-Tool-Executor/1.0',
      ...tool.headers,
    };

    // Add authentication headers
    if (tool.authentication) {
      switch (tool.authentication.type) {
        case 'bearer':
          headers['Authorization'] = `Bearer ${tool.authentication.token}`;
          break;
        case 'api_key':
          headers[tool.authentication.header || 'X-API-Key'] = tool.authentication.key;
          break;
        case 'basic':
          const credentials = Buffer.from(`${tool.authentication.username}:${tool.authentication.password}`).toString('base64');
          headers['Authorization'] = `Basic ${credentials}`;
          break;
      }
    }

    return headers;
  }

  private interpolateUrl(url: string, parameters: Record<string, any>): string {
    let interpolatedUrl = url;
    
    // Replace path parameters
    for (const [key, value] of Object.entries(parameters)) {
      interpolatedUrl = interpolatedUrl.replace(`{${key}}`, encodeURIComponent(String(value)));
    }

    // Add query parameters for GET requests
    const urlObj = new URL(interpolatedUrl);
    for (const [key, value] of Object.entries(parameters)) {
      if (!interpolatedUrl.includes(`{${key}}`)) {
        urlObj.searchParams.set(key, String(value));
      }
    }

    return urlObj.toString();
  }

  private calculateRetryDelay(attempt: number): number {
    // Exponential backoff with jitter
    const baseDelay = 1000; // 1 second
    const maxDelay = 30000; // 30 seconds
    const exponentialDelay = baseDelay * Math.pow(2, attempt - 1);
    const jitter = Math.random() * 1000; // Up to 1 second jitter
    
    return Math.min(exponentialDelay + jitter, maxDelay);
  }

  private isNonRetryableError(error: Error): boolean {
    const nonRetryablePatterns = [
      'validation failed',
      'unauthorized',
      'forbidden',
      'not found',
      'bad request',
    ];

    return nonRetryablePatterns.some(pattern => 
      error.message.toLowerCase().includes(pattern)
    );
  }

  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  private cacheResult(executionId: string, result: ToolExecutionResult): void {
    this.executionCache.set(executionId, result);
    
    // Clean up cache after 1 hour
    setTimeout(() => {
      this.executionCache.delete(executionId);
    }, 60 * 60 * 1000);
  }

  async getExecutionResult(executionId: string): Promise<ToolExecutionResult | null> {
    return this.executionCache.get(executionId) || null;
  }

  async getToolExecutionStats(toolId: string, organizationId: string): Promise<any> {
    // This would typically query a metrics database
    return {
      totalExecutions: 0,
      successRate: 0,
      averageExecutionTime: 0,
      errorRate: 0,
    };
  }
}
