import { Injectable, NotFoundException } from '@nestjs/common';
import { PrismaService } from '../database/prisma.service';
import { UserRole } from '@prisma/client';

interface CreateUserData {
  email: string;
  passwordHash: string;
  firstName: string;
  lastName: string;
  organizationSlug: string;
  organizationName?: string;
  role?: UserRole;
}

@Injectable()
export class UsersService {
  constructor(private readonly prisma: PrismaService) {}

  async create(data: CreateUserData) {
    // First, find or create organization
    let organization = await this.prisma.organization.findUnique({
      where: { slug: data.organizationSlug },
    });

    if (!organization) {
      organization = await this.prisma.organization.create({
        data: {
          name: data.organizationName || data.organizationSlug,
          slug: data.organizationSlug,
          description: `Organization for ${data.organizationName || data.organizationSlug}`,
        },
      });
    }

    // Create user
    const user = await this.prisma.user.create({
      data: {
        email: data.email,
        passwordHash: data.passwordHash,
        firstName: data.firstName,
        lastName: data.lastName,
        role: data.role || UserRole.USER,
        organizationId: organization.id,
      },
      include: {
        organization: true,
      },
    });

    return user;
  }

  async findById(id: string) {
    const user = await this.prisma.user.findUnique({
      where: { id },
      include: {
        organization: true,
      },
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    return user;
  }

  async findByEmail(email: string) {
    return this.prisma.user.findUnique({
      where: { email },
      include: {
        organization: true,
      },
    });
  }

  async updateLastLogin(id: string) {
    return this.prisma.user.update({
      where: { id },
      data: {
        lastLoginAt: new Date(),
      },
    });
  }

  async findByOrganization(organizationId: string) {
    return this.prisma.user.findMany({
      where: { organizationId },
      include: {
        organization: true,
      },
      orderBy: {
        createdAt: 'desc',
      },
    });
  }

  async updateUser(id: string, data: Partial<CreateUserData>) {
    const user = await this.prisma.user.update({
      where: { id },
      data: {
        firstName: data.firstName,
        lastName: data.lastName,
        role: data.role,
      },
      include: {
        organization: true,
      },
    });

    return user;
  }

  async deactivateUser(id: string) {
    return this.prisma.user.update({
      where: { id },
      data: {
        isActive: false,
      },
    });
  }

  async activateUser(id: string) {
    return this.prisma.user.update({
      where: { id },
      data: {
        isActive: true,
      },
    });
  }
}
