import { registerAs } from '@nestjs/config';

export default registerAs('security', () => ({
  // JWT Configuration
  jwt: {
    secret: process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-this-in-production',
    expiresIn: process.env.JWT_EXPIRES_IN || '24h',
    refreshExpiresIn: process.env.JWT_REFRESH_EXPIRES_IN || '7d',
  },

  // Rate Limiting
  rateLimit: {
    windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 15 * 60 * 1000, // 15 minutes
    max: parseInt(process.env.RATE_LIMIT_MAX) || 1000, // requests per window
    skipSuccessfulRequests: false,
    skipFailedRequests: false,
  },

  // CORS Configuration
  cors: {
    origin: process.env.FRONTEND_URL || 'http://localhost:3000',
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
    exposedHeaders: ['X-Total-Count', 'X-Page-Count'],
  },

  // Content Security Policy
  csp: {
    directives: {
      defaultSrc: ["'self'"],
      scriptSrc: ["'self'", "'unsafe-inline'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      imgSrc: ["'self'", "data:", "https:"],
      connectSrc: ["'self'", "wss:", "https:"],
      fontSrc: ["'self'", "data:"],
      objectSrc: ["'none'"],
      mediaSrc: ["'self'"],
      frameSrc: ["'none'"],
      baseUri: ["'self'"],
      formAction: ["'self'"],
    },
  },

  // Session Configuration
  session: {
    secret: process.env.SESSION_SECRET || 'your-session-secret-change-this',
    resave: false,
    saveUninitialized: false,
    cookie: {
      secure: process.env.NODE_ENV === 'production',
      httpOnly: true,
      maxAge: 24 * 60 * 60 * 1000, // 24 hours
      sameSite: 'strict',
    },
  },

  // Password Policy
  password: {
    minLength: 8,
    requireUppercase: true,
    requireLowercase: true,
    requireNumbers: true,
    requireSpecialChars: true,
    maxAttempts: 5,
    lockoutDuration: 15 * 60 * 1000, // 15 minutes
  },

  // API Security
  api: {
    maxRequestSize: '10mb',
    maxFileSize: '50mb',
    allowedFileTypes: [
      'image/jpeg',
      'image/png',
      'image/gif',
      'image/webp',
      'application/pdf',
      'text/plain',
      'text/csv',
      'application/json',
    ],
    requestTimeout: 30000, // 30 seconds
  },

  // Input Validation
  validation: {
    maxStringLength: 10000,
    maxArrayLength: 1000,
    maxObjectDepth: 10,
    allowedHtmlTags: [],
    allowedHtmlAttributes: [],
  },

  // Audit Logging
  audit: {
    enabled: process.env.AUDIT_LOGGING_ENABLED === 'true',
    logLevel: process.env.AUDIT_LOG_LEVEL || 'info',
    logSensitiveData: process.env.LOG_SENSITIVE_DATA === 'true',
    retentionDays: parseInt(process.env.AUDIT_RETENTION_DAYS) || 90,
  },

  // Encryption
  encryption: {
    algorithm: 'aes-256-gcm',
    keyLength: 32,
    ivLength: 16,
    tagLength: 16,
    saltLength: 32,
  },

  // Security Headers
  headers: {
    hsts: {
      maxAge: 31536000, // 1 year
      includeSubDomains: true,
      preload: true,
    },
    xFrameOptions: 'DENY',
    xContentTypeOptions: 'nosniff',
    xXssProtection: '1; mode=block',
    referrerPolicy: 'strict-origin-when-cross-origin',
    permissionsPolicy: 'geolocation=(), microphone=(), camera=()',
  },

  // WebSocket Security
  websocket: {
    maxConnections: 1000,
    maxMessageSize: 1024 * 1024, // 1MB
    heartbeatInterval: 30000, // 30 seconds
    connectionTimeout: 60000, // 1 minute
    rateLimitMessages: 100, // messages per minute
  },

  // Database Security
  database: {
    connectionTimeout: 30000,
    queryTimeout: 30000,
    maxConnections: 100,
    ssl: process.env.DATABASE_SSL === 'true',
    logQueries: process.env.LOG_DATABASE_QUERIES === 'true',
  },

  // Redis Security
  redis: {
    connectionTimeout: 10000,
    commandTimeout: 5000,
    maxRetriesPerRequest: 3,
    retryDelayOnFailover: 100,
    enableReadyCheck: true,
  },

  // File Upload Security
  upload: {
    maxFileSize: 50 * 1024 * 1024, // 50MB
    allowedMimeTypes: [
      'image/jpeg',
      'image/png',
      'image/gif',
      'image/webp',
      'application/pdf',
      'text/plain',
      'text/csv',
      'application/json',
    ],
    scanForViruses: process.env.VIRUS_SCANNING_ENABLED === 'true',
    quarantinePath: process.env.QUARANTINE_PATH || '/tmp/quarantine',
  },

  // API Rate Limiting by Endpoint
  endpointRateLimits: {
    '/api/auth/login': {
      windowMs: 15 * 60 * 1000, // 15 minutes
      max: 5, // 5 attempts per window
    },
    '/api/auth/register': {
      windowMs: 60 * 60 * 1000, // 1 hour
      max: 3, // 3 registrations per hour
    },
    '/api/tools/*/execute': {
      windowMs: 60 * 1000, // 1 minute
      max: 60, // 60 tool executions per minute
    },
    '/api/workflows/*/execute': {
      windowMs: 60 * 1000, // 1 minute
      max: 30, // 30 workflow executions per minute
    },
  },

  // Security Monitoring
  monitoring: {
    enableSecurityEvents: true,
    alertOnSuspiciousActivity: true,
    maxFailedAttempts: 10,
    suspiciousActivityWindow: 5 * 60 * 1000, // 5 minutes
    alertWebhook: process.env.SECURITY_ALERT_WEBHOOK,
  },

  // Development/Production Flags
  development: {
    enableDebugEndpoints: process.env.NODE_ENV === 'development',
    allowInsecureConnections: process.env.NODE_ENV === 'development',
    verboseErrorMessages: process.env.NODE_ENV === 'development',
    enableCorsForAllOrigins: process.env.NODE_ENV === 'development',
  },
}));
