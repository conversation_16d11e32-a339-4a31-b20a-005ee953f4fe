import { z } from 'zod';

// Common validation schemas
export const IdSchema = z.string().uuid('Invalid ID format');

export const PaginationSchema = z.object({
  page: z.number().int().min(1).default(1),
  limit: z.number().int().min(1).max(100).default(20),
  sortBy: z.string().optional(),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
});

export const SearchSchema = z.object({
  query: z.string().min(1).max(100),
  filters: z.record(z.any()).optional(),
});

// Agent schemas
export const CreateAgentSchema = z.object({
  name: z.string().min(1).max(100),
  description: z.string().max(500).optional(),
  prompt: z.string().min(1).max(5000),
  model: z.string().min(1),
  provider: z.string().min(1),
  temperature: z.number().min(0).max(2).default(0.7),
  maxTokens: z.number().int().min(1).max(4000).default(1000),
  systemPrompt: z.string().max(2000).optional(),
  settings: z.record(z.any()).optional(),
});

export const UpdateAgentSchema = CreateAgentSchema.partial();

// Tool schemas
export const CreateToolSchema = z.object({
  name: z.string().min(1).max(100),
  description: z.string().max(500).optional(),
  type: z.enum(['API', 'WEBHOOK', 'FUNCTION', 'DATABASE']),
  endpoint: z.string().url().optional(),
  method: z.enum(['GET', 'POST', 'PUT', 'PATCH', 'DELETE']).default('POST'),
  headers: z.record(z.string()).optional(),
  authentication: z.record(z.any()).optional(),
  inputSchema: z.record(z.any()),
  outputSchema: z.record(z.any()).optional(),
  timeout: z.number().int().min(1000).max(300000).default(30000),
  retryCount: z.number().int().min(0).max(10).default(3),
});

export const UpdateToolSchema = CreateToolSchema.partial();

// Workflow schemas
export const CreateWorkflowSchema = z.object({
  name: z.string().min(1).max(100),
  description: z.string().max(500).optional(),
  settings: z.record(z.any()).optional(),
  steps: z.array(z.object({
    order: z.number().int().min(1),
    type: z.enum(['AGENT', 'TOOL', 'CONDITION', 'LOOP']),
    agentId: z.string().uuid().optional(),
    toolId: z.string().uuid().optional(),
    settings: z.record(z.any()).optional(),
  })).optional(),
});

export const UpdateWorkflowSchema = CreateWorkflowSchema.partial();

// Session schemas
export const CreateSessionSchema = z.object({
  agentId: z.string().uuid().optional(),
  context: z.record(z.any()).optional(),
  memory: z.record(z.any()).optional(),
});

export const UpdateSessionSchema = z.object({
  context: z.record(z.any()).optional(),
  memory: z.record(z.any()).optional(),
});

// UAUI schemas
export const UAUIRequestSchema = z.object({
  sessionId: z.string().min(1),
  message: z.string().min(1).max(10000),
  appType: z.enum(['widget', 'dashboard', 'crm']),
  agentId: z.string().uuid().optional(),
  context: z.record(z.any()).optional(),
  metadata: z.record(z.any()).optional(),
});

export const StateUpdateSchema = z.object({
  state: z.record(z.any()),
  ttl: z.number().int().min(60).max(86400).optional(), // 1 minute to 24 hours
});

// Tool execution schemas
export const ToolExecutionSchema = z.object({
  parameters: z.record(z.any()),
  sessionId: z.string().optional(),
  timeout: z.number().int().min(1000).max(300000).optional(),
  retryCount: z.number().int().min(0).max(10).optional(),
  context: z.record(z.any()).optional(),
});

// Workflow execution schemas
export const WorkflowExecutionSchema = z.object({
  input: z.record(z.any()).optional(),
  context: z.record(z.any()).optional(),
  sessionId: z.string().optional(),
});

// User management schemas
export const UpdateUserSchema = z.object({
  firstName: z.string().min(1).max(50).optional(),
  lastName: z.string().min(1).max(50).optional(),
  role: z.enum(['ADMIN', 'DEVELOPER', 'USER', 'VIEWER']).optional(),
});

// Provider schemas
export const ProviderTestSchema = z.object({
  apiKey: z.string().min(1).optional(),
  model: z.string().min(1).optional(),
});

// Export types for TypeScript
export type IdDto = z.infer<typeof IdSchema>;
export type PaginationDto = z.infer<typeof PaginationSchema>;
export type SearchDto = z.infer<typeof SearchSchema>;
export type CreateAgentDto = z.infer<typeof CreateAgentSchema>;
export type UpdateAgentDto = z.infer<typeof UpdateAgentSchema>;
export type CreateToolDto = z.infer<typeof CreateToolSchema>;
export type UpdateToolDto = z.infer<typeof UpdateToolSchema>;
export type CreateWorkflowDto = z.infer<typeof CreateWorkflowSchema>;
export type UpdateWorkflowDto = z.infer<typeof UpdateWorkflowSchema>;
export type CreateSessionDto = z.infer<typeof CreateSessionSchema>;
export type UpdateSessionDto = z.infer<typeof UpdateSessionSchema>;
export type UAUIRequestDto = z.infer<typeof UAUIRequestSchema>;
export type StateUpdateDto = z.infer<typeof StateUpdateSchema>;
export type ToolExecutionDto = z.infer<typeof ToolExecutionSchema>;
export type WorkflowExecutionDto = z.infer<typeof WorkflowExecutionSchema>;
export type UpdateUserDto = z.infer<typeof UpdateUserSchema>;
export type ProviderTestDto = z.infer<typeof ProviderTestSchema>;
