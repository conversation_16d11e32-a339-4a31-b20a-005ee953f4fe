import { Injectable, CanActivate, ExecutionContext, ForbiddenException } from '@nestjs/common';
import { PrismaService } from '../../database/prisma.service';

@Injectable()
export class OrganizationGuard implements CanActivate {
  constructor(private readonly prisma: PrismaService) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const user = request.user;
    
    if (!user) {
      throw new ForbiddenException('User not authenticated');
    }

    // Extract resource ID from params (agent, tool, workflow, etc.)
    const resourceId = this.extractResourceId(request);
    
    if (!resourceId) {
      return true; // No resource to check
    }

    // Check if resource belongs to user's organization
    const hasAccess = await this.checkOrganizationAccess(user.organizationId, resourceId, request);
    
    if (!hasAccess) {
      throw new ForbiddenException('Access denied: Resource not found or belongs to different organization');
    }

    return true;
  }

  private extractResourceId(request: any): string | null {
    // Extract ID from various possible parameter names
    const possibleParams = ['id', 'agentId', 'toolId', 'workflowId', 'sessionId'];
    
    for (const param of possibleParams) {
      if (request.params[param]) {
        return request.params[param];
      }
    }

    return null;
  }

  private async checkOrganizationAccess(organizationId: string, resourceId: string, request: any): Promise<boolean> {
    const path = request.route?.path || request.url;
    
    try {
      // Determine resource type from path
      if (path.includes('/agents')) {
        const agent = await this.prisma.agent.findUnique({
          where: { id: resourceId },
          select: { organizationId: true },
        });
        return agent?.organizationId === organizationId;
      }
      
      if (path.includes('/tools')) {
        const tool = await this.prisma.tool.findUnique({
          where: { id: resourceId },
          select: { organizationId: true },
        });
        return tool?.organizationId === organizationId;
      }
      
      if (path.includes('/workflows')) {
        const workflow = await this.prisma.workflow.findUnique({
          where: { id: resourceId },
          select: { organizationId: true },
        });
        return workflow?.organizationId === organizationId;
      }
      
      if (path.includes('/sessions')) {
        const session = await this.prisma.session.findUnique({
          where: { id: resourceId },
          select: { organizationId: true },
        });
        return session?.organizationId === organizationId;
      }

      // Default to allowing access if we can't determine the resource type
      return true;
    } catch (error) {
      // If there's an error checking access, deny by default
      return false;
    }
  }
}
