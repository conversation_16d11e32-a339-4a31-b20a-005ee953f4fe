import { Injectable } from '@nestjs/common';
import { PrismaService } from './prisma.service';

@Injectable()
export class DatabaseService {
  constructor(private readonly prisma: PrismaService) {}

  async healthCheck(): Promise<{ status: string; timestamp: string }> {
    try {
      await this.prisma.$queryRaw`SELECT 1`;
      return {
        status: 'healthy',
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        timestamp: new Date().toISOString(),
      };
    }
  }

  async getStats(): Promise<any> {
    try {
      const [userCount, agentCount, toolCount, workflowCount] = await Promise.all([
        this.prisma.user.count(),
        this.prisma.agent.count(),
        this.prisma.tool.count(),
        this.prisma.workflow.count(),
      ]);

      return {
        users: userCount,
        agents: agentCount,
        tools: toolCount,
        workflows: workflowCount,
      };
    } catch (error) {
      return {
        error: 'Failed to get database stats',
      };
    }
  }
}
