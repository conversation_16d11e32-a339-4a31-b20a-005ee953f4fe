#!/usr/bin/env node

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🧪 SynapseAI Backend Test Suite');
console.log('================================\n');

// Test 1: Check if all required files exist
console.log('📁 Checking file structure...');
const requiredFiles = [
  'src/main.ts',
  'src/app.module.ts',
  'src/auth/auth.module.ts',
  'src/users/users.module.ts',
  'src/agents/agents.module.ts',
  'src/tools/tools.module.ts',
  'src/workflows/workflows.module.ts',
  'src/providers/providers.module.ts',
  'src/sessions/sessions.module.ts',
  'src/websocket/websocket.module.ts',
  'src/database/database.module.ts',
  'src/redis/redis.module.ts',
  'src/uaui/uaui.module.ts',
  'prisma/schema.prisma',
  'nest-cli.json',
  'tsconfig.json',
];

let missingFiles = [];
requiredFiles.forEach(file => {
  if (!fs.existsSync(path.join(__dirname, file))) {
    missingFiles.push(file);
  }
});

if (missingFiles.length > 0) {
  console.log('❌ Missing files:');
  missingFiles.forEach(file => console.log(`   - ${file}`));
} else {
  console.log('✅ All required files present');
}

// Test 2: Check TypeScript compilation
console.log('\n🔧 Testing TypeScript compilation...');
const tscProcess = spawn('npx', ['tsc', '--noEmit'], {
  cwd: __dirname,
  stdio: 'pipe'
});

tscProcess.stdout.on('data', (data) => {
  console.log(data.toString());
});

tscProcess.stderr.on('data', (data) => {
  console.log(data.toString());
});

tscProcess.on('close', (code) => {
  if (code === 0) {
    console.log('✅ TypeScript compilation successful');
  } else {
    console.log('❌ TypeScript compilation failed');
  }
  
  // Test 3: Check Prisma schema
  console.log('\n🗄️  Testing Prisma schema...');
  const prismaProcess = spawn('npx', ['prisma', 'validate', '--schema=prisma/schema.prisma'], {
    cwd: __dirname,
    stdio: 'pipe'
  });

  prismaProcess.stdout.on('data', (data) => {
    console.log(data.toString());
  });

  prismaProcess.stderr.on('data', (data) => {
    console.log(data.toString());
  });

  prismaProcess.on('close', (prismaCode) => {
    if (prismaCode === 0) {
      console.log('✅ Prisma schema valid');
    } else {
      console.log('❌ Prisma schema validation failed');
    }
    
    // Test 4: Check dependencies
    console.log('\n📦 Checking dependencies...');
    const packageJson = JSON.parse(fs.readFileSync(path.join(__dirname, '../package.json'), 'utf8'));
    
    const requiredDeps = [
      '@nestjs/core',
      '@nestjs/common',
      '@nestjs/platform-express',
      '@nestjs/websockets',
      '@nestjs/platform-socket.io',
      '@nestjs/jwt',
      '@nestjs/passport',
      '@nestjs/config',
      '@nestjs/throttler',
      '@nestjs/swagger',
      'passport',
      'passport-jwt',
      'passport-local',
      'bcryptjs',
      'class-validator',
      'class-transformer',
      'reflect-metadata',
      'rxjs',
      'socket.io',
      'redis',
      'ioredis',
      '@prisma/client',
      'prisma',
      'zod',
      'isomorphic-dompurify',
      'helmet',
      'express-rate-limit'
    ];
    
    const missingDeps = requiredDeps.filter(dep => 
      !packageJson.dependencies[dep] && !packageJson.devDependencies[dep]
    );
    
    if (missingDeps.length > 0) {
      console.log('❌ Missing dependencies:');
      missingDeps.forEach(dep => console.log(`   - ${dep}`));
    } else {
      console.log('✅ All required dependencies present');
    }
    
    // Test 5: Module structure validation
    console.log('\n🏗️  Validating module structure...');
    const modules = [
      'auth',
      'users', 
      'agents',
      'tools',
      'workflows',
      'providers',
      'sessions',
      'websocket',
      'database',
      'redis',
      'uaui'
    ];
    
    let moduleErrors = [];
    modules.forEach(module => {
      const modulePath = path.join(__dirname, 'src', module);
      const moduleFile = path.join(modulePath, `${module}.module.ts`);
      const serviceFile = path.join(modulePath, `${module}.service.ts`);
      const controllerFile = path.join(modulePath, `${module}.controller.ts`);
      
      if (!fs.existsSync(moduleFile)) {
        moduleErrors.push(`${module}.module.ts missing`);
      }
      
      // Some modules don't need controllers (database, redis)
      if (!['database', 'redis', 'websocket'].includes(module) && !fs.existsSync(controllerFile)) {
        moduleErrors.push(`${module}.controller.ts missing`);
      }
      
      // Some modules don't need services (websocket)
      if (!['websocket'].includes(module) && !fs.existsSync(serviceFile)) {
        moduleErrors.push(`${module}.service.ts missing`);
      }
    });
    
    if (moduleErrors.length > 0) {
      console.log('❌ Module structure issues:');
      moduleErrors.forEach(error => console.log(`   - ${error}`));
    } else {
      console.log('✅ Module structure valid');
    }
    
    // Test 6: Security configuration
    console.log('\n🔒 Checking security configuration...');
    const securityFiles = [
      'src/common/guards/rbac.guard.ts',
      'src/common/guards/organization.guard.ts',
      'src/common/pipes/validation.pipe.ts',
      'src/common/interceptors/sanitization.interceptor.ts',
      'src/common/interceptors/security-headers.interceptor.ts',
      'src/common/config/security.config.ts',
      'src/common/dto/common.dto.ts'
    ];
    
    const missingSecurityFiles = securityFiles.filter(file => 
      !fs.existsSync(path.join(__dirname, file))
    );
    
    if (missingSecurityFiles.length > 0) {
      console.log('❌ Missing security files:');
      missingSecurityFiles.forEach(file => console.log(`   - ${file}`));
    } else {
      console.log('✅ Security configuration complete');
    }
    
    // Summary
    console.log('\n📊 Test Summary');
    console.log('================');
    console.log(`Files: ${missingFiles.length === 0 ? '✅' : '❌'} ${requiredFiles.length - missingFiles.length}/${requiredFiles.length}`);
    console.log(`TypeScript: ${code === 0 ? '✅' : '❌'} Compilation`);
    console.log(`Prisma: ${prismaCode === 0 ? '✅' : '❌'} Schema`);
    console.log(`Dependencies: ${missingDeps.length === 0 ? '✅' : '❌'} ${requiredDeps.length - missingDeps.length}/${requiredDeps.length}`);
    console.log(`Modules: ${moduleErrors.length === 0 ? '✅' : '❌'} Structure`);
    console.log(`Security: ${missingSecurityFiles.length === 0 ? '✅' : '❌'} Configuration`);
    
    const allTestsPassed = missingFiles.length === 0 && 
                          code === 0 && 
                          prismaCode === 0 && 
                          missingDeps.length === 0 && 
                          moduleErrors.length === 0 && 
                          missingSecurityFiles.length === 0;
    
    console.log(`\n${allTestsPassed ? '🎉' : '⚠️'} Overall Status: ${allTestsPassed ? 'READY' : 'NEEDS ATTENTION'}`);
    
    if (allTestsPassed) {
      console.log('\n🚀 Backend is ready for development!');
      console.log('\nNext steps:');
      console.log('1. Set up your .env file with database and Redis credentials');
      console.log('2. Run: yarn db:generate');
      console.log('3. Run: yarn db:push (or yarn db:migrate)');
      console.log('4. Run: yarn db:seed');
      console.log('5. Start the backend: yarn backend:dev');
    }
  });
});
