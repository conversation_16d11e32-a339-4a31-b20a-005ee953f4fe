{"private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "backend:dev": "nest start --watch", "backend:build": "nest build", "backend:start": "node dist/main", "backend:start:dev": "nest start --watch", "backend:start:debug": "nest start --debug --watch", "backend:start:prod": "node dist/main", "db:generate": "prisma generate --schema=backend/prisma/schema.prisma", "db:push": "prisma db push --schema=backend/prisma/schema.prisma", "db:migrate": "prisma migrate dev --schema=backend/prisma/schema.prisma", "db:studio": "prisma studio --schema=backend/prisma/schema.prisma", "db:seed": "ts-node backend/prisma/seed.ts"}, "dependencies": {"@nestjs/common": "^10.0.0", "@nestjs/config": "^3.0.0", "@nestjs/core": "^10.0.0", "@nestjs/jwt": "^10.0.0", "@nestjs/passport": "^10.0.0", "@nestjs/platform-express": "^10.0.0", "@nestjs/platform-socket.io": "^10.0.0", "@nestjs/serve-static": "^4.0.0", "@nestjs/swagger": "^7.0.0", "@nestjs/throttler": "^5.0.0", "@nestjs/websockets": "^10.0.0", "@prisma/client": "^5.0.0", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-aspect-ratio": "^1.1.2", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-context-menu": "^2.2.6", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toast": "^1.2.2", "@supabase/ssr": "latest", "@supabase/supabase-js": "latest", "autoprefixer": "10.4.20", "bcryptjs": "^2.4.3", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "^1.0.4", "date-fns": "^4.1.0", "embla-carousel-react": "^8.5.2", "express-rate-limit": "^8.0.1", "helmet": "^8.1.0", "ioredis": "^5.3.0", "isomorphic-dompurify": "^2.26.0", "lucide-react": "^0.468.0", "nestjs-zod": "^3.0.0", "next": "14.2.23", "next-themes": "^0.2.1", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "prettier": "^3.3.3", "prisma": "^5.0.0", "radix-ui": "^1.1.3", "react": "^18", "react-day-picker": "^9.5.1", "react-dom": "^18", "react-hook-form": "^7.54.2", "react-resizable-panels": "^2.1.7", "redis": "^4.6.0", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.1", "socket.io": "^4.7.0", "stripe": "^17.6.0", "tempo-devtools": "^2.0.109", "vaul": "^1.1.2", "zod": "^3.22.0"}, "devDependencies": {"@types/bcryptjs": "^2.4.4", "@types/node": "^20", "@types/passport-jwt": "^3.0.9", "@types/passport-local": "^1.0.35", "@types/react": "^18", "@types/react-dom": "^18", "postcss": "^8", "tailwind-merge": "^2", "tailwindcss": "^3", "tailwindcss-animate": "^1", "ts-node": "^10.9.2", "typescript": "^5"}, "prisma": {"schema": "backend/prisma/schema.prisma", "seed": "ts-node backend/prisma/seed.ts"}}