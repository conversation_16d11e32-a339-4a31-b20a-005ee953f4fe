#!/bin/bash

# SynapseAI Backend Setup Script
# This script sets up the complete SynapseAI backend infrastructure

set -e

echo "🧠 SynapseAI Backend Setup"
echo "=========================="
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    print_error "Node.js is not installed. Please install Node.js 18.17.0 or higher."
    exit 1
fi

# Check Node.js version
NODE_VERSION=$(node --version | cut -d'v' -f2)
REQUIRED_VERSION="18.17.0"

if [ "$(printf '%s\n' "$REQUIRED_VERSION" "$NODE_VERSION" | sort -V | head -n1)" != "$REQUIRED_VERSION" ]; then
    print_error "Node.js version $NODE_VERSION is too old. Please install version $REQUIRED_VERSION or higher."
    exit 1
fi

print_status "Node.js version $NODE_VERSION is compatible"

# Check if Yarn is installed
if ! command -v yarn &> /dev/null; then
    print_warning "Yarn is not installed. Installing Yarn..."
    npm install -g yarn
fi

print_status "Yarn package manager is available"

# Install dependencies
print_info "Installing dependencies..."
yarn install

print_status "Dependencies installed successfully"

# Check if .env file exists
if [ ! -f ".env" ]; then
    print_warning ".env file not found. Creating from .env.example..."
    cp .env.example .env
    print_info "Please edit .env file with your database and Redis credentials"
else
    print_status ".env file already exists"
fi

# Generate Prisma client
print_info "Generating Prisma client..."
yarn db:generate

print_status "Prisma client generated successfully"

# Run backend tests
print_info "Running backend tests..."
cd backend && node test-backend.js
cd ..

print_status "All backend tests passed"

# Check if PostgreSQL is running (optional)
if command -v psql &> /dev/null; then
    print_info "PostgreSQL client found. Testing connection..."
    if psql -h localhost -U postgres -d postgres -c "SELECT 1;" &> /dev/null; then
        print_status "PostgreSQL connection successful"
        
        # Ask if user wants to set up database
        read -p "Do you want to set up the database now? (y/n): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            print_info "Setting up database..."
            yarn db:push
            print_status "Database schema created"
            
            read -p "Do you want to seed the database with sample data? (y/n): " -n 1 -r
            echo
            if [[ $REPLY =~ ^[Yy]$ ]]; then
                yarn db:seed
                print_status "Database seeded with sample data"
            fi
        fi
    else
        print_warning "Could not connect to PostgreSQL. Please ensure it's running and configured correctly."
    fi
else
    print_warning "PostgreSQL client not found. Please install PostgreSQL and configure your DATABASE_URL in .env"
fi

# Check if Redis is running (optional)
if command -v redis-cli &> /dev/null; then
    print_info "Redis client found. Testing connection..."
    if redis-cli ping &> /dev/null; then
        print_status "Redis connection successful"
    else
        print_warning "Could not connect to Redis. Please ensure it's running on localhost:6379"
    fi
else
    print_warning "Redis client not found. Please install Redis and ensure it's running"
fi

echo ""
echo "🎉 SynapseAI Backend Setup Complete!"
echo "===================================="
echo ""
echo "📋 Next Steps:"
echo "1. Configure your .env file with database and Redis credentials"
echo "2. Set up your AI provider API keys in .env (optional)"
echo "3. Start the backend: yarn backend:dev"
echo "4. Access API documentation: http://localhost:3001/api/docs"
echo ""
echo "🔧 Available Commands:"
echo "  yarn backend:dev          - Start development server"
echo "  yarn backend:build        - Build for production"
echo "  yarn backend:start        - Start production server"
echo "  yarn db:generate          - Generate Prisma client"
echo "  yarn db:push             - Push schema to database"
echo "  yarn db:migrate          - Run database migrations"
echo "  yarn db:seed             - Seed database with sample data"
echo "  yarn db:studio           - Open Prisma Studio"
echo ""
echo "📚 Documentation:"
echo "  Backend README: backend/README.md"
echo "  API Docs: http://localhost:3001/api/docs (when running)"
echo ""
echo "🔐 Default Credentials (after seeding):"
echo "  Admin: <EMAIL> / admin123"
echo "  Demo:  <EMAIL> / demo123"
echo ""

# Ask if user wants to start the backend now
read -p "Do you want to start the backend development server now? (y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    print_info "Starting SynapseAI backend..."
    yarn backend:dev
fi
